{"$type": "Grimoire.Botting.Configuration, Grimoire", "Commands": {"$type": "System.Collections.Generic.List`1[[Grimoire.Botting.IBotCommand, Grimoire]], mscorlib", "$values": [{"$type": "Grimoire.Botting.Commands.Map.CmdJ<PERSON>n, Grimoire", "Map": "originul-9099", "Cell": "Enter", "Pad": "Spawn"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdInTemp, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is in temp inventory", "Value1": "Essence of The Citadel", "Value2": "30"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdIndex, Grimoire", "Type": 1, "Index": 5}, {"$type": "Grimoire.Botting.Commands.Item.CmdEquip, Grimoire", "ItemName": "Legion Revenant"}, {"$type": "Grimoire.Botting.Commands.Combat.CmdSkillSet, Grimoire", "Name": "LR"}, {"$type": "Grimoire.Botting.Commands.Map.CmdMoveTo<PERSON>ell, Grimoire", "Cell": "r10", "Pad": "Top"}, {"$type": "Grimoire.Botting.Commands.Combat.CmdKill<PERSON>or, Grimoire", "Monster": "*", "ItemName": "Essence of The Citadel", "ItemType": 1, "Quantity": "30", "AfterKills": 1, "DelayAfterKill": 200}, {"$type": "Grimoire.Botting.Commands.Map.CmdMoveTo<PERSON>ell, Grimoire", "Cell": "r8", "Pad": "Bottom"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdBotDelay, Grimoire", "Delay": 1000}, {"$type": "Grimoire.Botting.Commands.Item.CmdEquip, Grimoire", "ItemName": "Void Highlord"}, {"$type": "Grimoire.Botting.Commands.Combat.CmdSkillSet, Grimoire", "Name": "VHL"}, {"$type": "Grimoire.Botting.Commands.Combat.CmdKill<PERSON>or, Grimoire", "Monster": "*", "ItemName": "Champion's <PERSON><PERSON><PERSON>", "ItemType": 1, "Quantity": "1", "AfterKills": 1, "DelayAfterKill": 500}]}, "Skills": {"$type": "System.Collections.Generic.List`1[[Grimoire.Game.Data.Skill, Grimoire]], mscorlib", "$values": [{"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "[LR]", "Type": 2}, {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "3: Depraved Empowerment", "Index": "3"}, {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "4: <PERSON><PERSON><PERSON>", "Index": "4"}, {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "2: <PERSON> Purgatory", "Index": "2"}, {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "1: Atramentous Shade", "Index": "1"}, {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "[VHL]", "Type": 2}, {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "4: Armageddon", "Index": "4"}, {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "2: <PERSON><PERSON>'s Gaze", "Index": "2"}, {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "1: <PERSON><PERSON><PERSON>", "Index": "1"}]}, "Quests": {"$type": "System.Collections.Generic.List`1[[Grimoire.Game.Data.Quest, Grimoire]], mscorlib", "$values": [{"$type": "Grimoire.Game.Data.Quest, Grimoire", "QuestID": 7890, "Text": "7890"}]}, "Author": "Author", "Description": "Description", "Boosts": {"$type": "System.Collections.Generic.List`1[[Grimoire.Game.Data.InventoryItem, Grimoire]], mscorlib", "$values": []}, "Drops": {"$type": "System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib", "$values": ["Essence of The Citadel", "Champion's <PERSON><PERSON><PERSON>", "Fiend Em<PERSON>m"]}, "Server": {"$type": "Grimoire.Game.Data.Server, Grimoire", "sName": "Twilly", "iPort": 59464}, "SkillDelay": 100, "ExitCombatBeforeRest": true, "ExitCombatBeforeQuest": true, "EnablePickup": true, "RelogDelay": 5000, "BotDelay": 1000, "SkipDelayIndexIf": true, "InfiniteAttackRange": true, "DisableAnimations": true, "WalkSpeed": 8, "NotifyUponDrop": {"$type": "System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib", "$values": []}, "RestMp": 60, "RestHp": 60, "RestartUponDeath": true}