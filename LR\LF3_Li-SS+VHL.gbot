{"$type": "Grimoire.Botting.Configuration, Grimoire", "Commands": {"$type": "System.Collections.Generic.List`1[[Grimoire.Botting.IBotCommand, Grimoire]], mscorlib", "$values": [{"$type": "Grimoire.Botting.Commands.Map.CmdMoveTo<PERSON>ell, Grimoire", "Cell": "Blank", "Pad": "Spawn"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdDelay, Grimoire", "Delay": 1000}, {"$type": "Grimoire.Botting.Commands.Item.CmdEquip, Grimoire", "ItemName": "Scarlet Sorceress"}, {"$type": "Grimoire.Botting.Commands.Combat.CmdSkillSet, Grimoire", "Name": "SS"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdNotInInventory, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is not in inventory", "Value1": "Legion Token", "Value2": "4000"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "LEGION TOKEN"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdNotInInventory, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is not in inventory", "Value1": "<PERSON><PERSON>'s Favor", "Value2": "300"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "DAGE FAVOR"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdNotInInventory, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is not in inventory", "Value1": "Hooded Legion Cowl", "Value2": "1"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "LEGION COWL"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdNotInInventory, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is not in inventory", "Value1": "Emblem of Dage", "Value2": "1"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "EMBLEM OF DAGE"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdNotInInventory, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is not in inventory", "Value1": "Diamond Token of Dage", "Value2": "30"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "DIAMOND OF DAGE"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdNotInInventory, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is not in inventory", "Value1": "Dark Token", "Value2": "100"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "DARK TOKEN"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdRestart, Grimoire"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "LEGION TOKEN"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdInBank, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is in bank", "Value1": "Shogun <PERSON>", "Value2": "1"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "LT-SPP"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdInInventory, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is in inventory", "Value1": "Shogun <PERSON>", "Value2": "1"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "LT-SPP"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "LT-LEGIONARENA"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "LT-SPP"}, {"$type": "Grimoire.Botting.Commands.Item.CmdBankTransfer, Grimoire", "TransferFromBank": true, "ItemName": "Shogun <PERSON>"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdProvoke, Grimoire", "Set": true}, {"$type": "Grimoire.Botting.Commands.Map.CmdJ<PERSON>n, Grimoire", "Map": "fotia", "Cell": "Enter", "Pad": "Spawn"}, {"$type": "Grimoire.Botting.Commands.Quest.CmdAcceptQuest, Grimoire", "Quest": {"$type": "Grimoire.Game.Data.Quest, Grimoire", "QuestID": 5755, "Text": ""}}, {"$type": "Grimoire.Botting.Commands.Combat.CmdKill<PERSON>or, Grimoire", "Monster": "*", "KillPriority": "", "AfterKills": 1, "QuestId": "5755", "DelayAfterKill": 500}, {"$type": "Grimoire.Botting.Commands.Quest.CmdCompleteQuest, Grimoire", "Quest": {"$type": "Grimoire.Game.Data.Quest, Grimoire", "QuestID": 5755, "Text": ""}, "CompleteTry": 1, "InBlank": true, "Delay": 1000}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdNotInInventory, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is not in inventory", "Value1": "Legion Token", "Value2": "4000"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdIndex, Grimoire", "Index": 5, "IndexString": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdProvoke, Grimoire"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdRestart, Grimoire"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "LT-LEGIONARENA"}, {"$type": "Grimoire.Botting.Commands.Item.CmdEquip, Grimoire", "ItemName": "Void Highlord"}, {"$type": "Grimoire.Botting.Commands.Combat.CmdSkillSet, Grimoire", "Name": "VHL"}, {"$type": "Grimoire.Botting.Commands.Map.CmdJ<PERSON>n, Grimoire", "Map": "legionarena", "Cell": "Boss", "Pad": "Left"}, {"$type": "Grimoire.Botting.Commands.Quest.CmdAcceptQuest, Grimoire", "Quest": {"$type": "Grimoire.Game.Data.Quest, Grimoire", "QuestID": 6743, "Text": ""}}, {"$type": "Grimoire.Botting.Commands.Combat.CmdKill<PERSON>or, Grimoire", "Monster": "*", "ItemName": "<PERSON><PERSON><PERSON>' Brooch", "ItemType": 1, "Quantity": "1", "KillPriority": "", "AfterKills": 1, "DelayAfterKill": 500}, {"$type": "Grimoire.Botting.Commands.Quest.CmdCompleteQuest, Grimoire", "Quest": {"$type": "Grimoire.Game.Data.Quest, Grimoire", "QuestID": 6743, "Text": ""}, "CompleteTry": 1, "InBlank": true, "Delay": 1000}, {"$type": "Grimoire.Botting.Commands.Misc.CmdRestart, Grimoire"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "DAGE FAVOR"}, {"$type": "Grimoire.Botting.Commands.Map.CmdJ<PERSON>n, Grimoire", "Map": "underworld", "Cell": "r8", "Pad": "Left"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdProvoke, Grimoire", "Set": true}, {"$type": "Grimoire.Botting.Commands.Combat.CmdKill<PERSON>or, Grimoire", "Monster": "*", "ItemName": "<PERSON><PERSON>'s Favor", "Quantity": "300", "KillPriority": "", "IsGetDrops": true, "AfterKills": 3, "DelayAfterKill": 500}, {"$type": "Grimoire.Botting.Commands.Misc.CmdProvoke, Grimoire"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdRestart, Grimoire"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "EMBLEM OF DAGE"}, {"$type": "Grimoire.Botting.Commands.Map.CmdJ<PERSON>n, Grimoire", "Map": "shadowblast", "Cell": "r10", "Pad": "Left"}, {"$type": "Grimoire.Botting.Commands.Quest.CmdAcceptQuest, Grimoire", "Quest": {"$type": "Grimoire.Game.Data.Quest, Grimoire", "QuestID": 4742, "Text": ""}}, {"$type": "Grimoire.Botting.Commands.Combat.CmdKill<PERSON>or, Grimoire", "Monster": "<PERSON><PERSON><PERSON>", "ItemName": "Legion Seal", "Quantity": "25", "KillPriority": "", "AfterKills": 3, "DelayAfterKill": 100}, {"$type": "Grimoire.Botting.Commands.Combat.CmdKill<PERSON>or, Grimoire", "Monster": "Carnage", "ItemName": "Gem of Mastery", "Quantity": "1", "KillPriority": "", "AfterKills": 3, "DelayAfterKill": 100}, {"$type": "Grimoire.Botting.Commands.Quest.CmdCompleteQuest, Grimoire", "Quest": {"$type": "Grimoire.Game.Data.Quest, Grimoire", "QuestID": 4742, "Text": ""}, "CompleteTry": 1, "InBlank": true, "Delay": 1000}, {"$type": "Grimoire.Botting.Commands.Misc.CmdRestart, Grimoire"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "DIAMOND OF DAGE"}, {"$type": "Grimoire.Botting.Commands.Quest.CmdAcceptQuest, Grimoire", "Quest": {"$type": "Grimoire.Game.Data.Quest, Grimoire", "QuestID": 4743, "Text": "", "IsInProgress": true}}, {"$type": "Grimoire.Botting.Commands.Combat.CmdShortHunt, Grimoire", "Map": "aqlesson-a", "Cell": "Frame9", "Pad": "Right", "Monster": "*", "MonId": "", "ItemName": "Carnax Eye", "ItemType": 1, "Quantity": "1", "AfterKills": 1, "QuestId": "", "DelayAfterKill": 500}, {"$type": "Grimoire.Botting.Commands.Combat.CmdShortHunt, Grimoire", "Map": "deepchaos-a", "Cell": "Frame4", "Pad": "Left", "Monster": "*", "MonId": "", "ItemName": "Kathool Tentacle", "ItemType": 1, "Quantity": "1", "AfterKills": 1, "QuestId": "", "DelayAfterKill": 500}, {"$type": "Grimoire.Botting.Commands.Combat.CmdShortHunt, Grimoire", "Map": "lair-a", "Cell": "End", "Pad": "Left", "Monster": "*", "MonId": "", "ItemName": "Red Dragon's Fang", "ItemType": 1, "Quantity": "1", "AfterKills": 1, "QuestId": "", "DelayAfterKill": 500}, {"$type": "Grimoire.Botting.Commands.Combat.CmdShortHunt, Grimoire", "Map": "d<PERSON>sson-a", "Cell": "r12", "Pad": "Right", "Monster": "*", "MonId": "", "ItemName": "<PERSON><PERSON><PERSON>'s Bones", "ItemType": 1, "Quantity": "1", "AfterKills": 1, "QuestId": "", "DelayAfterKill": 500}, {"$type": "Grimoire.Botting.Commands.Combat.CmdShortHunt, Grimoire", "Map": "bloodtitan", "Cell": "Enter", "Pad": "Spawn", "Monster": "*", "MonId": "", "ItemName": "Blood Titan's Blade", "ItemType": 1, "Quantity": "1", "AfterKills": 1, "QuestId": "", "DelayAfterKill": 500}, {"$type": "Grimoire.Botting.Commands.Map.CmdJ<PERSON>n, Grimoire", "Map": "citadel", "Cell": "m22", "Pad": "Left"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdDelay, Grimoire", "Delay": 500}, {"$type": "Grimoire.Botting.Commands.Combat.CmdShortHunt, Grimoire", "Map": "tercessuinotlim-a", "Cell": "m2", "Pad": "Left", "Monster": "*", "MonId": "", "ItemName": "Defeated Makai", "Quantity": "25", "AfterKills": 3, "QuestId": "", "DelayAfterKill": 500}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdNotInInventory, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is not in inventory", "Value1": "Diamond Token of Dage", "Value2": "30"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "DIAMOND OF DAGE"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdRestart, Grimoire"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "DARK TOKEN"}, {"$type": "Grimoire.Botting.Commands.Map.CmdJ<PERSON>n, Grimoire", "Map": "seraphicwardage-a", "Cell": "Enter", "Pad": "Spawn"}, {"$type": "Grimoire.Botting.Commands.Combat.Cmd<PERSON>ill, Grimoire", "Monster": "*", "KillPriority": ""}, {"$type": "Grimoire.Botting.Commands.Combat.Cmd<PERSON>ill, Grimoire", "Monster": "*", "KillPriority": ""}, {"$type": "Grimoire.Botting.Commands.Combat.Cmd<PERSON>ill, Grimoire", "Monster": "*", "KillPriority": ""}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdNotInInventory, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is not in inventory", "Value1": "Dark Token", "Value2": "100"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdIndex, Grimoire", "Index": 4, "IndexString": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdRestart, Grimoire"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "LEGION COWL"}, {"$type": "Grimoire.Botting.Commands.Map.CmdJ<PERSON>n, Grimoire", "Map": "underworld", "Cell": "s1", "Pad": "Left"}, {"$type": "Grimoire.Botting.Commands.Item.CmdBuy, Grimoire", "ShopId": 216, "ItemName": "Hooded Legion Cowl"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdRestart, Grimoire"}]}, "Skills": {"$type": "System.Collections.Generic.List`1[[Grimoire.Game.Data.Skill, Grimoire]], mscorlib", "$values": [{"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "SS", "Type": 2}, {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "<PERSON>uine P<PERSON>", "Index": "1", "Type": 1, "SType": 1, "SafeValue": 60}, {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "Mirroring Arcane", "Index": "2"}, {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "Malevolence", "Index": "3"}, {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "Crimson Ritual", "Index": "4", "Type": 1, "SType": 1, "SafeValue": 60}, {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "VHL", "Type": 2}, {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "<PERSON>uine P<PERSON>", "Index": "1", "Type": 1, "SType": 1, "SafeValue": 60}, {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "Mirroring Arcane", "Index": "2"}, {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "Malevolence", "Index": "3", "Type": 1, "SType": 1, "SafeValue": 60}, {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "Crimson Ritual", "Index": "4"}]}, "Quests": {"$type": "System.Collections.Generic.List`1[[Grimoire.Game.Data.Quest, Grimoire]], mscorlib", "$values": [{"$type": "Grimoire.Game.Data.Quest, Grimoire", "QuestID": 6248, "CompleteInBlank": true, "Text": "6248 [InBlank] [InBlank]", "IsInProgress": true}, {"$type": "Grimoire.Game.Data.Quest, Grimoire", "QuestID": 6249, "CompleteInBlank": true, "Text": "6249 [InBlank] [InBlank]", "IsInProgress": true}, {"$type": "Grimoire.Game.Data.Quest, Grimoire", "QuestID": 6899, "CompleteInBlank": true, "Text": "6899 [InBlank] [InBlank]", "IsInProgress": true}, {"$type": "Grimoire.Game.Data.Quest, Grimoire", "QuestID": 4743, "CompleteInBlank": true, "Text": "4743", "IsInProgress": true}]}, "Author": "Froztt13", "Description": "{\\rtf1\\ansi\\ansicpg1252\\deff0\\deflang2057{\\fonttbl{\\f0\\fnil\\fcharset0 Microsoft Sans Serif;}}\r\n{\\colortbl ;\\red220\\green220\\blue220;}\r\n\\viewkind4\\uc1\\pard\\cf1\\b\\f0\\fs29 Requirment:\\par\r\n- complete story line /seraphicwardage\\par\r\n\\par\r\n}\r\n", "Boosts": {"$type": "System.Collections.Generic.List`1[[Grimoire.Game.Data.InventoryItem, Grimoire]], mscorlib", "$values": []}, "Drops": {"$type": "System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib", "$values": ["Legion Token", "Legion Seal", "Gem of Mastery", "Emblem of Dage", "Diamond Token of Dage", "Defeated Makai", "Dark Token", "Exalted Crown", "<PERSON><PERSON><PERSON>' Brooch"]}, "Server": {"$type": "Grimoire.Game.Data.Server, Grimoire", "sName": "Galanoth", "iPort": 2223}, "ExitCombatBeforeRest": true, "ExitCombatBeforeQuest": true, "EnablePickup": true, "EnableRejection": true, "AutoRelogin": true, "RelogDelay": 5000, "BotDelay": 1500, "SkipDelayIndexIf": true, "InfiniteAttackRange": true, "SkipCutscenes": true, "DisableAnimations": true, "WalkSpeed": 8, "NotifyUponDrop": {"$type": "System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib", "$values": []}, "RestMp": 60, "RestHp": 60, "RestartUponDeath": true, "Items": {"$type": "System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib", "$values": []}, "DropDelay": 1000}