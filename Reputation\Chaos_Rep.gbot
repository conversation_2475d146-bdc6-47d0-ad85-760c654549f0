{"$type": "Grimoire.Botting.Configuration, Grimoire", "Commands": {"$type": "System.Collections.Generic.List`1[[Grimoire.Botting.IBotCommand, Grimoire]], mscorlib", "$values": [{"$type": "Grimoire.Botting.Commands.Map.CmdJ<PERSON>n, Grimoire", "Map": "mountdoomskull", "Cell": "b5", "Pad": "Left"}, {"$type": "Grimoire.Botting.Commands.Combat.CmdKill<PERSON>or, Grimoire", "Monster": "*", "AfterKills": 1, "QuestId": "3594", "DelayAfterKill": 500}]}, "Skills": {"$type": "System.Collections.Generic.List`1[[Grimoire.Game.Data.Skill, Grimoire]], mscorlib", "$values": [{"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "3: Stasis", "Index": "3"}, {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "Healing Word", "Index": "2", "Type": 1, "SafeValue": 60}]}, "Quests": {"$type": "System.Collections.Generic.List`1[[Grimoire.Game.Data.Quest, Grimoire]], mscorlib", "$values": [{"$type": "Grimoire.Game.Data.Quest, Grimoire", "QuestID": 3594, "Text": "3594 [InBlank]", "CompleteInBlank": true}]}, "Author": "Author", "Description": "Description", "Boosts": {"$type": "System.Collections.Generic.List`1[[Grimoire.Game.Data.InventoryItem, Grimoire]], mscorlib", "$values": []}, "Drops": {"$type": "System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib", "$values": ["Item name"]}, "Server": {"$type": "Grimoire.Game.Data.Server, Grimoire", "sName": "Galanoth", "iPort": 21312}, "SkillDelay": 100, "ExitCombatBeforeRest": true, "ExitCombatBeforeQuest": true, "DropDelay": 1000, "EnableRejection": true, "AutoRelogin": true, "RelogDelay": 5000, "RelogRetryUponFailure": true, "BotDelay": 1000, "SkipDelayIndexIf": true, "InfiniteAttackRange": true, "ProvokeMonsters": true, "LagKiller": true, "DisableAnimations": true, "WalkSpeed": 8, "NotifyUponDrop": {"$type": "System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib", "$values": []}, "RestMp": 60, "RestHp": 60, "RestartUponDeath": true}