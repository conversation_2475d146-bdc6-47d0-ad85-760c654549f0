{"$type": "Grimoire.Botting.Configuration, Grimoire", "Commands": {"$type": "System.Collections.Generic.List`1[[Grimoire.Botting.IBotCommand, Grimoire]], mscorlib", "$values": [{"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdSetVar, Grimoire", "Tag": "Misc", "Text": "Set Temporary Variable", "Value1": "PLAYER", "Value2": "Remake"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdMapIs, Grimoire", "Tag": "Map", "Text": "Map is", "Value1": "ultratyndarius", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "GO"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdBlank3, G<PERSON>oire", "Text": "...", "Alpha": 1, "R": 220, "G": 220, "B": 220}, {"$type": "Grimoire.Botting.Commands.Misc.CmdPacket, Grimoire", "Packet": "{\"t\":\"xt\",\"b\":{\"r\":-1,\"o\":{\"cmd\":\"updateQuest\",\"iValue\":30,\"iIndex\":412}}}", "SpamTimes": 1, "Delay": 1000, "ForClient": true}, {"$type": "Grimoire.Botting.Commands.Map.CmdJ<PERSON>n, Grimoire", "Map": "ultratyndarius-9099", "Cell": "Enter", "Pad": "Spawn"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdBlank3, G<PERSON>oire", "Text": "...", "Alpha": 1, "R": 220, "G": 220, "B": 220}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "GO"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdMonsterInRoom, Grimoire", "Tag": "Monster", "Text": "Is in room", "Value1": "Ultra Fire Orb", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "A-UFO"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdMonsterInRoom, Grimoire", "Tag": "Monster", "Text": "Is in room", "Value1": "Ultra Avatar Tyndarius", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "A-AVATAR"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdMonsterNotInRoom, Grimoire", "Tag": "Monster", "Text": "Is not in room", "Value1": "Ultra Avatar Tyndarius", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "LOG"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdBlank3, G<PERSON>oire", "Text": "...", "Alpha": 1, "R": 220, "G": 220, "B": 220}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "S"}, {"$type": "Grimoire.Botting.Commands.Combat.CmdUseSkill, Grimoire", "Skill": {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "<PERSON>’n Parry", "Index": "1"}, "Targeted": true}, {"$type": "Grimoire.Botting.Commands.Combat.CmdUseSkill, Grimoire", "Skill": {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "Flintlock Barrage", "Index": "2"}, "Targeted": true}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdManaGreaterThan, Grimoire", "Tag": "This player", "Text": "Mana is greater than", "Value1": "40", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Combat.CmdUseSkill, Grimoire", "Skill": {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "Commander's Rally", "Index": "3"}, "Targeted": true}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdManaGreaterThan, Grimoire", "Tag": "This player", "Text": "Mana is greater than", "Value1": "50", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Combat.CmdUseSkill, Grimoire", "Skill": {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "<PERSON>lock Fury", "Index": "4"}, "Targeted": true}, {"$type": "Grimoire.Botting.Commands.Combat.CmdUseSkill, Grimoire", "Skill": {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "DragonH<PERSON><PERSON>tre", "Index": "5"}, "Targeted": true}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "GO"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdBlank3, G<PERSON>oire", "Text": "...", "Alpha": 1, "R": 220, "G": 220, "B": 220}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "A-UFO"}, {"$type": "Grimoire.Botting.Commands.Combat.CmdAttack, Grimoire", "Monster": "Ultra Fire Orb", "UseSkill": true}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "S"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdBlank3, G<PERSON>oire", "Text": "...", "Alpha": 1, "R": 220, "G": 220, "B": 220}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "A-AVATAR"}, {"$type": "Grimoire.Botting.Commands.Combat.CmdAttack, Grimoire", "Monster": "Ultra Avatar Tyndarius", "UseSkill": true}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "S"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdBlank3, G<PERSON>oire", "Text": "...", "Alpha": 1, "R": 220, "G": 220, "B": 220}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "LOG"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLog, Grimoire", "Text": "Monster Killed !"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdDelay, Grimoire", "Delay": 1000}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdMonsterNotInRoom, Grimoire", "Tag": "Monster", "Text": "Is not in room", "Value1": "Ultra Avatar Tyndarius", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdIndex, Grimoire", "Index": 2, "IndexString": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "GO"}]}, "Skills": {"$type": "System.Collections.Generic.List`1[[Grimoire.Game.Data.Skill, Grimoire]], mscorlib", "$values": []}, "Quests": {"$type": "System.Collections.Generic.List`1[[Grimoire.Game.Data.Quest, Grimoire]], mscorlib", "$values": []}, "Author": "Froztt13", "Description": "need <PERSON><PERSON><PERSON><PERSON>", "Boosts": {"$type": "System.Collections.Generic.List`1[[Grimoire.Game.Data.InventoryItem, Grimoire]], mscorlib", "$values": []}, "Drops": {"$type": "System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib", "$values": []}, "Server": {"$type": "Grimoire.Game.Data.Server, Grimoire", "sName": "Twilly", "iPort": 5588}, "ExitCombatBeforeRest": true, "ExitCombatBeforeQuest": true, "EnablePickup": true, "RelogDelay": 5000, "SkipDelayIndexIf": true, "InfiniteAttackRange": true, "DisableAnimations": true, "WalkSpeed": 8, "NotifyUponDrop": {"$type": "System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib", "$values": []}, "RestMp": 60, "RestHp": 60, "RestartUponDeath": true, "Items": {"$type": "System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib", "$values": []}, "DropDelay": 500, "FollowCheck": true, "FollowName": "fierce ward"}