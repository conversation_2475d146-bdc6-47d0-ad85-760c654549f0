{"$type": "Grimoire.Botting.Configuration, Grimoire", "Commands": {"$type": "System.Collections.Generic.List`1[[Grimoire.Botting.IBotCommand, Grimoire]], mscorlib", "$values": [{"$type": "Grimoire.Botting.Commands.Combat.CmdAttack, Grimoire", "Monster": "*", "UseSkill": true}, {"$type": "Grimoire.Botting.Commands.Combat.CmdUseSkill, Grimoire", "Skill": {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "Lingering Light", "Index": "1"}, "Targeted": true}, {"$type": "Grimoire.Botting.Commands.Combat.CmdUseSkill, Grimoire", "Skill": {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "Wicked Purgatory", "Index": "2"}, "Force": true}, {"$type": "Grimoire.Botting.Commands.Combat.CmdUseSkill, Grimoire", "Skill": {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "Light Blast", "Index": "3"}, "Force": true}, {"$type": "Grimoire.Botting.Commands.Combat.CmdUseSkill, Grimoire", "Skill": {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "Burned by Light", "Index": "4"}, "Targeted": true}]}, "Skills": {"$type": "System.Collections.Generic.List`1[[Grimoire.Game.Data.Skill, Grimoire]], mscorlib", "$values": []}, "Quests": {"$type": "System.Collections.Generic.List`1[[Grimoire.Game.Data.Quest, Grimoire]], mscorlib", "$values": []}, "Author": "", "Description": "{\\rtf1\\ansi\\ansicpg1252\\deff0\\deflang2057{\\fonttbl{\\f0\\fnil\\fcharset0 Microsoft Sans Serif;}}\r\n{\\colortbl ;\\red220\\green220\\blue220;}\r\n\\viewkind4\\uc1\\pard\\cf1\\b\\f0\\fs29\\par\r\n}\r\n", "Boosts": {"$type": "System.Collections.Generic.List`1[[Grimoire.Game.Data.InventoryItem, Grimoire]], mscorlib", "$values": []}, "Drops": {"$type": "System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib", "$values": []}, "Server": {"$type": "Grimoire.Game.Data.Server, Grimoire", "sName": "Twilly", "iPort": 5588}, "SkillDelay": 100, "ExitCombatBeforeRest": true, "ExitCombatBeforeQuest": true, "EnablePickup": true, "RelogDelay": 5000, "SkipDelayIndexIf": true, "InfiniteAttackRange": true, "WalkSpeed": 8, "NotifyUponDrop": {"$type": "System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib", "$values": []}, "RestMp": 60, "RestHp": 60, "RestartUponDeath": true, "Items": {"$type": "System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib", "$values": []}, "DropDelay": 500, "AntiCounter": true, "FollowCheck": true, "FollowName": "remake"}