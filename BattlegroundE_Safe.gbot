{"$type": "Grimoire.Botting.Configuration, Grimoire", "Commands": {"$type": "System.Collections.Generic.List`1[[Grimoire.Botting.IBotCommand, Grimoire]], mscorlib", "$values": [{"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdSetVar, Grimoire", "Tag": "Misc", "Text": "Set Temporary Variable", "Value1": "ITEM", "Value2": "Bone Dust"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdInt, Grimoire", "Int": "COUNT"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "FARM"}, {"$type": "Grimoire.Botting.Commands.Map.CmdJ<PERSON>n, Grimoire", "Map": "BattlegroundE", "Cell": "r2", "Pad": "Center"}, {"$type": "Grimoire.Botting.Commands.Combat.Cmd<PERSON>ill, Grimoire", "Monster": "*"}, {"$type": "Grimoire.Botting.Commands.Combat.Cmd<PERSON>ill, Grimoire", "Monster": "*"}, {"$type": "Grimoire.Botting.Commands.Combat.Cmd<PERSON>ill, Grimoire", "Monster": "*"}, {"$type": "Grimoire.Botting.Commands.Combat.Cmd<PERSON>ill, Grimoire", "Monster": "*"}, {"$type": "Grimoire.Botting.Commands.Combat.Cmd<PERSON>ill, Grimoire", "Monster": "*"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdInt, Grimoire", "type": 1, "Int": "COUNT"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdIntLessThan, Grimoire", "Tag": "Misc", "Text": "Int Lesser Than", "Value1": "COUNT", "Value2": "10"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "FARM"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "SAVE PROGRESS"}, {"$type": "Grimoire.Botting.Commands.Map.CmdMoveTo<PERSON>ell, Grimoire", "Cell": "Blank", "Pad": "Spawn"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdDelay, Grimoire", "Delay": 1500}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdInInventory, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is in inventory", "Value1": "[ITEM]", "Value2": "1"}, {"$type": "Grimoire.Botting.Commands.Item.CmdBankTransfer, Grimoire", "ItemName": "[ITEM]"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdInBank, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is in bank", "Value1": "[ITEM]", "Value2": "1"}, {"$type": "Grimoire.Botting.Commands.Item.CmdBankTransfer, Grimoire", "TransferFromBank": true, "ItemName": "[ITEM]"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLog, Grimoire", "Text": "[{TIME: 24}] Progress saved !"}]}, "Skills": {"$type": "System.Collections.Generic.List`1[[Grimoire.Game.Data.Skill, Grimoire]], mscorlib", "$values": [{"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "<PERSON>uine P<PERSON>", "Index": "1", "Type": 1, "SType": 1, "SafeValue": 50}, {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "Mirroring Arcane", "Index": "2"}, {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "Malevolence", "Index": "3"}, {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "Crimson Ritual", "Index": "4", "Type": 1, "SType": 1, "SafeValue": 50}]}, "Quests": {"$type": "System.Collections.Generic.List`1[[Grimoire.Game.Data.Quest, Grimoire]], mscorlib", "$values": [{"$type": "Grimoire.Game.Data.Quest, Grimoire", "QuestID": 3991, "CompleteInBlank": true, "Text": "3991"}, {"$type": "Grimoire.Game.Data.Quest, Grimoire", "QuestID": 3992, "CompleteInBlank": true, "Text": "3992 [InBlank]"}]}, "Author": "Froztt13", "Description": "{\\rtf1\\ansi\\ansicpg1252\\deff0\\deflang2057{\\fonttbl{\\f0\\fnil\\fcharset0 Microsoft Sans Serif;}}\r\n{\\colortbl ;\\red220\\green220\\blue220;}\r\n\\viewkind4\\uc1\\pard\\cf1\\b\\f0\\fs29 BattlegroudE with Save Progress !\\par\r\n}\r\n", "Boosts": {"$type": "System.Collections.Generic.List`1[[Grimoire.Game.Data.InventoryItem, Grimoire]], mscorlib", "$values": []}, "Drops": {"$type": "System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib", "$values": ["Item name", "Battleground E Opponent Defeated"]}, "Server": {"$type": "Grimoire.Game.Data.Server, Grimoire", "sName": "Artix", "iPort": 53465}, "SkillDelay": 100, "ExitCombatBeforeRest": true, "ExitCombatBeforeQuest": true, "EnableRejection": true, "AutoRelogin": true, "RelogDelay": 5000, "RelogRetryUponFailure": true, "BotDelay": 500, "SkipDelayIndexIf": true, "InfiniteAttackRange": true, "ProvokeMonsters": true, "DisableAnimations": true, "WalkSpeed": 8, "NotifyUponDrop": {"$type": "System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib", "$values": []}, "RestMp": 60, "RestHp": 60, "RestartUponDeath": true, "Items": {"$type": "System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib", "$values": []}, "AFK": true, "DropDelay": 1000}