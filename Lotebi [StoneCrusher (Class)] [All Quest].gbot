{"$type": "Grimoire.Botting.Configuration, Grimoire", "Commands": {"$type": "System.Collections.Generic.List`1[[Grimoire.Botting.IBotCommand, Grimoire]], mscorlib", "$values": [{"$type": "Grimoire.Botting.Commands.Misc.CmdIndex, Grimoire", "Type": 1, "Index": 8}, {"$type": "Grimoire.Botting.Commands.Misc.CmdBlank2, G<PERSON>oire", "Text": " sdfsdf"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdBlank3, G<PERSON>oire", "Text": "", "R": 220, "G": 220, "B": 220}, {"$type": "Grimoire.Botting.Commands.Misc.CmdBlank2, G<PERSON>oire", "Text": "[210,210,210]by <PERSON><PERSON><PERSON>"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdBlank2, G<PERSON>oire", "Text": "[100,100,100]Join Channel"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdBlank2, G<PERSON>oire", "Text": "[250,250,250]Youtube.com/LOTEBI"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdBlank2, G<PERSON>oire", "Text": " sdfsdf"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdBlank2, G<PERSON>oire", "Text": " sdfsdf"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdBotDelay2, Grimoire", "Delay": "10"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdIndex, Grimoire", "Type": 1, "Index": 8}, {"$type": "Grimoire.Botting.Commands.Misc.CmdBlank2, G<PERSON>oire", "Text": " sdfsdf"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdBlank2, G<PERSON>oire", "Text": "[250,250,250]▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdBlank2, G<PERSON>oire", "Text": "[39,39,39]▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇▇"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdBlank2, G<PERSON>oire", "Text": " "}, {"$type": "Grimoire.Botting.Commands.Misc.CmdBlank2, G<PERSON>oire", "Text": "[255,255,255] Lotebi#3984"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdBlank2, G<PERSON>oire", "Text": "[255,255,255]  My Discord        "}, {"$type": "Grimoire.Botting.Commands.Misc.CmdBlank3, G<PERSON>oire", "Text": "", "R": 220, "G": 220, "B": 220}, {"$type": "Grimoire.Botting.Commands.Misc.CmdBlank2, G<PERSON>oire", "Text": "[255,255,255] Change 1e99 to 1 for Public"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "Settings"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdSetVar, Grimoire", "Tag": "Misc", "Text": "Set Variable", "Value1": "Lotebi Room.", "Value2": "1e99"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdBlank3, G<PERSON>oire", "Text": "", "R": 220, "G": 220, "B": 220}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdSetVar, Grimoire", "Tag": "Misc", "Text": "Set Variable", "Value1": "QuestID", "Value2": "BLANK"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdChange, Grimoire", "Text": "LOTEBI BOT"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdChange, Grimoire", "Guild": true, "Text": "YOUTUBE.COM/LOTEBI"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "Setup"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "----------------------------------------------------"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdBlank2, G<PERSON>oire", "Text": " sdfsdf"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "Setup"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdInt, Grimoire", "Int": "a"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdInt, Grimoire", "Int": "requirements"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdSetVar, Grimoire", "Tag": "Misc", "Text": "Set Variable", "Value1": "ItemName", "Value2": "BLANK"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdSetVar, Grimoire", "Tag": "Misc", "Text": "Set Variable", "Value1": "ShopID", "Value2": "BLANK"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdSetVar, Grimoire", "Tag": "Misc", "Text": "Set Variable", "Value1": "MapName", "Value2": "BLANK"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdSetVar, Grimoire", "Tag": "Misc", "Text": "Set Variable", "Value1": "MapShop", "Value2": "BLANK"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdSetVar, Grimoire", "Tag": "Misc", "Text": "Set Variable", "Value1": "DropCount", "Value2": "BLANK"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdSetVar, Grimoire", "Tag": "Misc", "Text": "Set Variable", "Value1": "DropName", "Value2": "BLANK"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdSetVar, Grimoire", "Tag": "Misc", "Text": "Set Variable", "Value1": "ItemDropCount", "Value2": "BLANK"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdSetVar, Grimoire", "Tag": "Misc", "Text": "Set Variable", "Value1": "Cell1", "Value2": "BLANK"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdSetVar, Grimoire", "Tag": "Misc", "Text": "Set Variable", "Value1": "Cell2", "Value2": "BLANK"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdSetVar, Grimoire", "Tag": "Misc", "Text": "Set Variable", "Value1": "MonsterName", "Value2": "BLANK"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdToggleProvoke, Grimoire"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdBotDelay2, Grimoire", "Delay": "200"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdPacket2, Grimoire", "Packet": "%xt%zm%getQuests%594208%4439%\n", "Delay": 500}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "CHECK"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdBlank2, G<PERSON>oire", "Text": " sdfsdf"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "CHECK"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdIntIs, Grimoire", "Tag": "Misc", "Text": "Int is", "Value1": "requirements", "Value2": "0"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "REQUIREMENTS"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdUpdateVar, Grimoire", "Tag": "Misc", "Text": "Update Variable", "Value1": "ShopID", "Value2": "1210"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdUpdateVar, Grimoire", "Tag": "Misc", "Text": "Update Variable", "Value1": "MapShop", "Value2": "Gaiazor"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdUpdateVar, Grimoire", "Tag": "Misc", "Text": "Update Variable", "Value1": "DropName", "Value2": "StoneCrusher"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdUpdateVar, Grimoire", "Tag": "Misc", "Text": "Update Variable", "Value1": "DropCount", "Value2": "1"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdNotInInventory, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is not in inventory", "Value1": "[DropName]", "Value2": "[DropCount]"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "[DropName]"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "END"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdBlank2, G<PERSON>oire", "Text": " sdfsdf"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "Requirements"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdLevelLessThan, Grimoire", "Tag": "This player", "Text": "Level is less than", "Value1": "10", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdStopBotWithMessage, Grimoire", "Message": "Required lvl: 10"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdFactionRankLessThan, Grimoire", "Tag": "This player", "Text": "Faction Rank is less than", "Value1": "Brightoak", "Value2": "10"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "BrightoakRep"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdFactionRankLessThan, Grimoire", "Tag": "This player", "Text": "Faction Rank is less than", "Value1": "Arcangrove", "Value2": "10"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "ArcangroveRep"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdFactionRankLessThan, Grimoire", "Tag": "This player", "Text": "Faction Rank is less than", "Value1": "<PERSON><PERSON><PERSON>", "Value2": "10"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "MythsongRep"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdInt, Grimoire", "Int": "requirements", "Value": 1}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "CHECK"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdBlank2, G<PERSON>oire", "Text": " sdfsdf"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "StoneCrusher"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdMapIsNot, Grimoire", "Tag": "Map", "Text": "Map is not", "Value1": "[MapShop]", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Map.CmdJoin2, Grimoire", "Map": "[MapShop]", "Room": "[Lotebi Room.]", "Cell": "Enter", "Pad": "Spawn"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdDelay, Grimoire", "Delay": 200}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdInInventory, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is in inventory", "Value1": "Shaman <PERSON><PERSON>", "Value2": "1"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdIndex, Grimoire", "Type": 1, "Index": 3}, {"$type": "Grimoire.Botting.Commands.Item.CmdBuy2, Grimoire", "ShopId": "[ShopID]", "ItemName": "Shaman <PERSON><PERSON>"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "CHECK"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdDelay, Grimoire", "Delay": 200}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdInInventory, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is in inventory", "Value1": "Earth's <PERSON>", "Value2": "1"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdIndex, Grimoire", "Type": 1, "Index": 3}, {"$type": "Grimoire.Botting.Commands.Item.CmdBuy2, Grimoire", "ShopId": "[ShopID]", "ItemName": "Earth's <PERSON>"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "CHECK"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdDelay, Grimoire", "Delay": 200}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdInInventory, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is in inventory", "Value1": "StoneCrusher", "Value2": "1"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdIndex, Grimoire", "Type": 1, "Index": 2}, {"$type": "Grimoire.Botting.Commands.Item.CmdBuy2, Grimoire", "ShopId": "[ShopID]", "ItemName": "StoneCrusher"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdDelay, Grimoire", "Delay": 200}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "CHECK"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdBlank2, G<PERSON>oire", "Text": " sdfsdf"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "ArcangroveRep"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdPacket2, Grimoire", "Packet": "%xt%zm%getQuests%9366%4909%794%797%798%800%", "Delay": 500}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdUpdateVar, Grimoire", "Tag": "Misc", "Text": "Update Variable", "Value1": "QuestID", "Value2": "797"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdQuestNotInProgress, Grimoire", "Tag": "Quest", "Text": "Quest is not in progress", "Value1": "[QuestID]", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Quest.CmdAcceptQuest2, Grimoire", "QuestID": "[QuestID]"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdQuestCompleted, Grimoire", "Tag": "Quest", "Text": "Quest can be turned in", "Value1": "[QuestID]", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdIndex, Grimoire", "Type": 1, "Index": 7}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdMapIsNot, Grimoire", "Tag": "Map", "Text": "Map is not", "Value1": "Arcangrove", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Map.CmdJoin2, Grimoire", "Map": "Arcangrove", "Room": "[Lotebi Room.]", "Cell": "Back", "Pad": "Left"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdCellIsNot, Grimoire", "Tag": "Map", "Text": "Cell is not", "Value1": "Back", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Map.CmdMoveTo<PERSON>ell, Grimoire", "Cell": "Back", "Pad": "Left"}, {"$type": "Grimoire.Botting.Commands.Combat.Cmd<PERSON>ill, Grimoire", "Monster": "*"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdIndex, Grimoire", "Index": 7}, {"$type": "Grimoire.Botting.Commands.Quest.CmdCompleteQuest2, G<PERSON>oire", "QuestID": "[QuestID]", "ItemID": ""}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdFactionRankLessThan, Grimoire", "Tag": "This player", "Text": "Faction Rank is less than", "Value1": "Brightoak", "Value2": "10"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "4667"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "CHECK"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdBlank3, G<PERSON>oire", "Text": "", "R": 220, "G": 220, "B": 220}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "MythsongRep"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdPacket2, Grimoire", "Packet": "%xt%zm%getQuests%9366%4829%", "Delay": 500}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdUpdateVar, Grimoire", "Tag": "Misc", "Text": "Update Variable", "Value1": "QuestID", "Value2": "4829"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdQuestNotInProgress, Grimoire", "Tag": "Quest", "Text": "Quest is not in progress", "Value1": "[QuestID]", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Quest.CmdAcceptQuest2, Grimoire", "QuestID": "[QuestID]"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdQuestCompleted, Grimoire", "Tag": "Quest", "Text": "Quest can be turned in", "Value1": "[QuestID]", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdIndex, Grimoire", "Type": 1, "Index": 7}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdMapIsNot, Grimoire", "Tag": "Map", "Text": "Map is not", "Value1": "Beehive", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Map.CmdJoin2, Grimoire", "Map": "Beehive", "Room": "[Lotebi Room.]", "Cell": "r2", "Pad": "Left"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdCellIsNot, Grimoire", "Tag": "Map", "Text": "Cell is not", "Value1": "r2", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Map.CmdMoveTo<PERSON>ell, Grimoire", "Cell": "r2", "Pad": "Left"}, {"$type": "Grimoire.Botting.Commands.Combat.Cmd<PERSON>ill, Grimoire", "Monster": "*"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdIndex, Grimoire", "Index": 7}, {"$type": "Grimoire.Botting.Commands.Quest.CmdCompleteQuest2, G<PERSON>oire", "QuestID": "[QuestID]", "ItemID": ""}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdFactionRankLessThan, Grimoire", "Tag": "This player", "Text": "Faction Rank is less than", "Value1": "Arcangrove", "Value2": "10"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "ArcangroveRep"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "CHECK"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdBlank3, G<PERSON>oire", "Text": "", "R": 220, "G": 220, "B": 220}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "BrightoakRep"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdPacket2, Grimoire", "Packet": "%xt%zm%getQuests%9366%4909%794%797%798%800%5121%5123%5124%3757%8736%2933%2934%2935%4667%4637%4638%4639%4640%4641%4642%4643%4644%4645%", "Delay": 500}, {"$type": "Grimoire.Botting.Commands.Misc.CmdPacket2, Grimoire", "Packet": "%xt%zm%getQuests%35989%4659%4660%4661%4662%4663%4664%4665%4666%4668%\n", "Delay": 500}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdUpdateVar, Grimoire", "Tag": "Misc", "Text": "Update Variable", "Value1": "QuestID", "Value2": "4667"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdQuestAvailable, Grimoire", "Tag": "Quest", "Text": "Quest is available", "Value1": "[QuestID]", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "[QuestID]"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdUpdateVar, Grimoire", "Tag": "Misc", "Text": "Update Variable", "Value1": "QuestID", "Value2": "4666"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdQuestAvailable, Grimoire", "Tag": "Quest", "Text": "Quest is available", "Value1": "[QuestID]", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "[QuestID]"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdUpdateVar, Grimoire", "Tag": "Misc", "Text": "Update Variable", "Value1": "QuestID", "Value2": "4665"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdQuestAvailable, Grimoire", "Tag": "Quest", "Text": "Quest is available", "Value1": "[QuestID]", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "[QuestID]"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdUpdateVar, Grimoire", "Tag": "Misc", "Text": "Update Variable", "Value1": "QuestID", "Value2": "4664"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdQuestAvailable, Grimoire", "Tag": "Quest", "Text": "Quest is available", "Value1": "[QuestID]", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "[QuestID]"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdUpdateVar, Grimoire", "Tag": "Misc", "Text": "Update Variable", "Value1": "QuestID", "Value2": "4663"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdQuestAvailable, Grimoire", "Tag": "Quest", "Text": "Quest is available", "Value1": "[QuestID]", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "[QuestID]"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdUpdateVar, Grimoire", "Tag": "Misc", "Text": "Update Variable", "Value1": "QuestID", "Value2": "4662"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdQuestAvailable, Grimoire", "Tag": "Quest", "Text": "Quest is available", "Value1": "[QuestID]", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "[QuestID]"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdUpdateVar, Grimoire", "Tag": "Misc", "Text": "Update Variable", "Value1": "QuestID", "Value2": "4661"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdQuestAvailable, Grimoire", "Tag": "Quest", "Text": "Quest is available", "Value1": "[QuestID]", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "[QuestID]"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdUpdateVar, Grimoire", "Tag": "Misc", "Text": "Update Variable", "Value1": "QuestID", "Value2": "4660"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdQuestAvailable, Grimoire", "Tag": "Quest", "Text": "Quest is available", "Value1": "[QuestID]", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "[QuestID]"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdUpdateVar, Grimoire", "Tag": "Misc", "Text": "Update Variable", "Value1": "QuestID", "Value2": "4645"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdQuestAvailable, Grimoire", "Tag": "Quest", "Text": "Quest is available", "Value1": "[QuestID]", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "[QuestID]"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdUpdateVar, Grimoire", "Tag": "Misc", "Text": "Update Variable", "Value1": "QuestID", "Value2": "4644"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdQuestAvailable, Grimoire", "Tag": "Quest", "Text": "Quest is available", "Value1": "[QuestID]", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "[QuestID]"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdUpdateVar, Grimoire", "Tag": "Misc", "Text": "Update Variable", "Value1": "QuestID", "Value2": "4643"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdQuestAvailable, Grimoire", "Tag": "Quest", "Text": "Quest is available", "Value1": "[QuestID]", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "[QuestID]"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdUpdateVar, Grimoire", "Tag": "Misc", "Text": "Update Variable", "Value1": "QuestID", "Value2": "4642"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdQuestAvailable, Grimoire", "Tag": "Quest", "Text": "Quest is available", "Value1": "[QuestID]", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "[QuestID]"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdUpdateVar, Grimoire", "Tag": "Misc", "Text": "Update Variable", "Value1": "QuestID", "Value2": "4641"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdQuestAvailable, Grimoire", "Tag": "Quest", "Text": "Quest is available", "Value1": "[QuestID]", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "[QuestID]"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdUpdateVar, Grimoire", "Tag": "Misc", "Text": "Update Variable", "Value1": "QuestID", "Value2": "4640"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdQuestAvailable, Grimoire", "Tag": "Quest", "Text": "Quest is available", "Value1": "[QuestID]", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "[QuestID]"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdUpdateVar, Grimoire", "Tag": "Misc", "Text": "Update Variable", "Value1": "QuestID", "Value2": "4639"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdQuestAvailable, Grimoire", "Tag": "Quest", "Text": "Quest is available", "Value1": "[QuestID]", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "[QuestID]"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdUpdateVar, Grimoire", "Tag": "Misc", "Text": "Update Variable", "Value1": "QuestID", "Value2": "4638"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdQuestAvailable, Grimoire", "Tag": "Quest", "Text": "Quest is available", "Value1": "[QuestID]", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "[QuestID]"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdUpdateVar, Grimoire", "Tag": "Misc", "Text": "Update Variable", "Value1": "QuestID", "Value2": "4637"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdQuestAvailable, Grimoire", "Tag": "Quest", "Text": "Quest is available", "Value1": "[QuestID]", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "[QuestID]"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdFactionRankLessThan, Grimoire", "Tag": "This player", "Text": "Faction Rank is less than", "Value1": "Brightoak", "Value2": "10"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "4667"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "CHECK"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdBlank2, G<PERSON>oire", "Text": " sdfsdf"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "4637"}, {"$type": "Grimoire.Botting.Commands.Item.CmdMapItem2, Grimoire", "ItemId": "3935"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdPacket2, Grimoire", "Packet": "%xt%zm%equipItem%64634%32057%\n", "Delay": 1500}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdQuestNotInProgress, Grimoire", "Tag": "Quest", "Text": "Quest is not in progress", "Value1": "[QuestID]", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Quest.CmdAcceptQuest2, Grimoire", "QuestID": "[QuestID]"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdQuestCompleted, Grimoire", "Tag": "Quest", "Text": "Quest can be turned in", "Value1": "[QuestID]", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdIndex, Grimoire", "Type": 1, "Index": 7}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdMapIsNot, Grimoire", "Tag": "Map", "Text": "Map is not", "Value1": "Rivensylth", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Map.CmdJoin2, Grimoire", "Map": "Rivensylth", "Room": "[Lotebi Room.]", "Cell": "Enter", "Pad": "Spawn"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdCellIsNot, Grimoire", "Tag": "Map", "Text": "Cell is not", "Value1": "Enter", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Map.CmdMoveTo<PERSON>ell, Grimoire", "Cell": "Enter", "Pad": "Spawn"}, {"$type": "Grimoire.Botting.Commands.Item.CmdMapItem2, Grimoire", "ItemId": "3944"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdIndex, Grimoire", "Index": 7}, {"$type": "Grimoire.Botting.Commands.Quest.CmdCompleteQuest2, G<PERSON>oire", "QuestID": "[QuestID]", "ItemID": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "BrightoakRep"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdBlank3, G<PERSON>oire", "Text": "", "R": 220, "G": 220, "B": 220}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "4638"}, {"$type": "Grimoire.Botting.Commands.Item.CmdMapItem2, Grimoire", "ItemId": "3935"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdPacket2, Grimoire", "Packet": "%xt%zm%equipItem%64634%32057%\n", "Delay": 1500}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdQuestNotInProgress, Grimoire", "Tag": "Quest", "Text": "Quest is not in progress", "Value1": "[QuestID]", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Quest.CmdAcceptQuest2, Grimoire", "QuestID": "[QuestID]"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdQuestCompleted, Grimoire", "Tag": "Quest", "Text": "Quest can be turned in", "Value1": "[QuestID]", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdIndex, Grimoire", "Type": 1, "Index": 7}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdMapIsNot, Grimoire", "Tag": "Map", "Text": "Map is not", "Value1": "Rivensylth", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Map.CmdJoin2, Grimoire", "Map": "Rivensylth", "Room": "[Lotebi Room.]", "Cell": "o2", "Pad": "left"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdCellIsNot, Grimoire", "Tag": "Map", "Text": "Cell is not", "Value1": "o2", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Map.CmdMoveTo<PERSON>ell, Grimoire", "Cell": "o2", "Pad": "Left"}, {"$type": "Grimoire.Botting.Commands.Combat.Cmd<PERSON>ill, Grimoire", "Monster": "Cave"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdIndex, Grimoire", "Index": 7}, {"$type": "Grimoire.Botting.Commands.Quest.CmdCompleteQuest2, G<PERSON>oire", "QuestID": "[QuestID]", "ItemID": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "BrightoakRep"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdBlank3, G<PERSON>oire", "Text": "", "R": 220, "G": 220, "B": 220}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "4639"}, {"$type": "Grimoire.Botting.Commands.Item.CmdMapItem2, Grimoire", "ItemId": "3935"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdPacket2, Grimoire", "Packet": "%xt%zm%equipItem%64634%32057%\n", "Delay": 1500}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdQuestNotInProgress, Grimoire", "Tag": "Quest", "Text": "Quest is not in progress", "Value1": "[QuestID]", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Quest.CmdAcceptQuest2, Grimoire", "QuestID": "[QuestID]"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdQuestCompleted, Grimoire", "Tag": "Quest", "Text": "Quest can be turned in", "Value1": "[QuestID]", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdIndex, Grimoire", "Type": 1, "Index": 7}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdMapIsNot, Grimoire", "Tag": "Map", "Text": "Map is not", "Value1": "Rivensylth", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Map.CmdJoin2, Grimoire", "Map": "Rivensylth", "Room": "[Lotebi Room.]", "Cell": "o2", "Pad": "left"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdCellIsNot, Grimoire", "Tag": "Map", "Text": "Cell is not", "Value1": "o2", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Map.CmdMoveTo<PERSON>ell, Grimoire", "Cell": "o2", "Pad": "Left"}, {"$type": "Grimoire.Botting.Commands.Item.CmdMapItem2, Grimoire", "ItemId": "3945"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdIndex, Grimoire", "Index": 7}, {"$type": "Grimoire.Botting.Commands.Quest.CmdCompleteQuest2, G<PERSON>oire", "QuestID": "[QuestID]", "ItemID": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "BrightoakRep"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdBlank3, G<PERSON>oire", "Text": "", "R": 220, "G": 220, "B": 220}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "4640"}, {"$type": "Grimoire.Botting.Commands.Item.CmdMapItem2, Grimoire", "ItemId": "3935"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdPacket2, Grimoire", "Packet": "%xt%zm%equipItem%64634%32057%\n", "Delay": 1500}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdQuestNotInProgress, Grimoire", "Tag": "Quest", "Text": "Quest is not in progress", "Value1": "[QuestID]", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Quest.CmdAcceptQuest2, Grimoire", "QuestID": "[QuestID]"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdQuestCompleted, Grimoire", "Tag": "Quest", "Text": "Quest can be turned in", "Value1": "[QuestID]", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdIndex, Grimoire", "Type": 1, "Index": 7}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdMapIsNot, Grimoire", "Tag": "Map", "Text": "Map is not", "Value1": "Rivensylth", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Map.CmdJoin2, Grimoire", "Map": "Rivensylth", "Room": "[Lotebi Room.]", "Cell": "o4", "Pad": "left"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdCellIsNot, Grimoire", "Tag": "Map", "Text": "Cell is not", "Value1": "o4", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Map.CmdMoveTo<PERSON>ell, Grimoire", "Cell": "o4", "Pad": "Left"}, {"$type": "Grimoire.Botting.Commands.Combat.Cmd<PERSON>ill, Grimoire", "Monster": "*"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdIndex, Grimoire", "Index": 7}, {"$type": "Grimoire.Botting.Commands.Quest.CmdCompleteQuest2, G<PERSON>oire", "QuestID": "[QuestID]", "ItemID": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "BrightoakRep"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdBlank3, G<PERSON>oire", "Text": "", "R": 220, "G": 220, "B": 220}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "4641"}, {"$type": "Grimoire.Botting.Commands.Item.CmdMapItem2, Grimoire", "ItemId": "3935"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdPacket2, Grimoire", "Packet": "%xt%zm%equipItem%64634%32057%\n", "Delay": 1500}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdQuestNotInProgress, Grimoire", "Tag": "Quest", "Text": "Quest is not in progress", "Value1": "[QuestID]", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Quest.CmdAcceptQuest2, Grimoire", "QuestID": "[QuestID]"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdQuestCompleted, Grimoire", "Tag": "Quest", "Text": "Quest can be turned in", "Value1": "[QuestID]", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdIndex, Grimoire", "Type": 1, "Index": 15}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdInTemp, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is in temp inventory", "Value1": "Tree Sap", "Value2": "4"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdIndex, Grimoire", "Type": 1, "Index": 5}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdMapIsNot, Grimoire", "Tag": "Map", "Text": "Map is not", "Value1": "Rivensylth", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Map.CmdJoin2, Grimoire", "Map": "Rivensylth", "Room": "[Lotebi Room.]", "Cell": "o3", "Pad": "left"}, {"$type": "Grimoire.Botting.Commands.Item.CmdMapItem2, Grimoire", "ItemId": "3948"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdIndex, Grimoire", "Index": 5}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdInTemp, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is in temp inventory", "Value1": "<PERSON><PERSON>", "Value2": "3"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdIndex, Grimoire", "Type": 1, "Index": 7}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdMapIsNot, Grimoire", "Tag": "Map", "Text": "Map is not", "Value1": "pines", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Map.CmdJoin2, Grimoire", "Map": "pines", "Room": "[Lotebi Room.]", "Cell": "Mountain", "Pad": "left"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdCellIsNot, Grimoire", "Tag": "Map", "Text": "Cell is not", "Value1": "Mountain", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Map.CmdMoveTo<PERSON>ell, Grimoire", "Cell": "Mountain", "Pad": "Left"}, {"$type": "Grimoire.Botting.Commands.Combat.Cmd<PERSON>ill, Grimoire", "Monster": "*"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdIndex, Grimoire", "Index": 7}, {"$type": "Grimoire.Botting.Commands.Quest.CmdCompleteQuest2, G<PERSON>oire", "QuestID": "[QuestID]", "ItemID": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "BrightoakRep"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdBlank3, G<PERSON>oire", "Text": "", "R": 220, "G": 220, "B": 220}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "4642"}, {"$type": "Grimoire.Botting.Commands.Item.CmdMapItem2, Grimoire", "ItemId": "3935"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdPacket2, Grimoire", "Packet": "%xt%zm%equipItem%64634%32057%\n", "Delay": 1500}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdQuestNotInProgress, Grimoire", "Tag": "Quest", "Text": "Quest is not in progress", "Value1": "[QuestID]", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Quest.CmdAcceptQuest2, Grimoire", "QuestID": "[QuestID]"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdQuestCompleted, Grimoire", "Tag": "Quest", "Text": "Quest can be turned in", "Value1": "[QuestID]", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdIndex, Grimoire", "Type": 1, "Index": 7}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdMapIsNot, Grimoire", "Tag": "Map", "Text": "Map is not", "Value1": "Rivensylth", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Map.CmdJoin2, Grimoire", "Map": "Rivensylth", "Room": "[Lotebi Room.]", "Cell": "o3", "Pad": "left"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdCellIsNot, Grimoire", "Tag": "Map", "Text": "Cell is not", "Value1": "o3", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Map.CmdMoveTo<PERSON>ell, Grimoire", "Cell": "o3", "Pad": "Left"}, {"$type": "Grimoire.Botting.Commands.Combat.Cmd<PERSON>ill, Grimoire", "Monster": "*"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdIndex, Grimoire", "Index": 7}, {"$type": "Grimoire.Botting.Commands.Quest.CmdCompleteQuest2, G<PERSON>oire", "QuestID": "[QuestID]", "ItemID": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "BrightoakRep"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdBlank3, G<PERSON>oire", "Text": "", "R": 220, "G": 220, "B": 220}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "4643"}, {"$type": "Grimoire.Botting.Commands.Item.CmdMapItem2, Grimoire", "ItemId": "3935"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdPacket2, Grimoire", "Packet": "%xt%zm%equipItem%64634%32057%\n", "Delay": 1500}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdQuestNotInProgress, Grimoire", "Tag": "Quest", "Text": "Quest is not in progress", "Value1": "[QuestID]", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Quest.CmdAcceptQuest2, Grimoire", "QuestID": "[QuestID]"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdQuestCompleted, Grimoire", "Tag": "Quest", "Text": "Quest can be turned in", "Value1": "[QuestID]", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdIndex, Grimoire", "Type": 1, "Index": 7}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdMapIsNot, Grimoire", "Tag": "Map", "Text": "Map is not", "Value1": "Rivensylth", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Map.CmdJoin2, Grimoire", "Map": "Rivensylth", "Room": "[Lotebi Room.]", "Cell": "o3", "Pad": "left"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdCellIsNot, Grimoire", "Tag": "Map", "Text": "Cell is not", "Value1": "o3", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Map.CmdMoveTo<PERSON>ell, Grimoire", "Cell": "o3", "Pad": "Left"}, {"$type": "Grimoire.Botting.Commands.Item.CmdMapItem2, Grimoire", "ItemId": "3946"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdIndex, Grimoire", "Index": 7}, {"$type": "Grimoire.Botting.Commands.Quest.CmdCompleteQuest2, G<PERSON>oire", "QuestID": "[QuestID]", "ItemID": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "BrightoakRep"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdBlank3, G<PERSON>oire", "Text": "", "R": 220, "G": 220, "B": 220}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "4644"}, {"$type": "Grimoire.Botting.Commands.Item.CmdMapItem2, Grimoire", "ItemId": "3935"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdPacket2, Grimoire", "Packet": "%xt%zm%equipItem%64634%32057%\n", "Delay": 1500}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdQuestNotInProgress, Grimoire", "Tag": "Quest", "Text": "Quest is not in progress", "Value1": "[QuestID]", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Quest.CmdAcceptQuest2, Grimoire", "QuestID": "[QuestID]"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdQuestCompleted, Grimoire", "Tag": "Quest", "Text": "Quest can be turned in", "Value1": "[QuestID]", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdIndex, Grimoire", "Type": 1, "Index": 7}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdMapIsNot, Grimoire", "Tag": "Map", "Text": "Map is not", "Value1": "Rivensylth", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Map.CmdJoin2, Grimoire", "Map": "Rivensylth", "Room": "[Lotebi Room.]", "Cell": "Boss", "Pad": "left"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdCellIsNot, Grimoire", "Tag": "Map", "Text": "Cell is not", "Value1": "Boss", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Map.CmdMoveTo<PERSON>ell, Grimoire", "Cell": "Boss", "Pad": "Left"}, {"$type": "Grimoire.Botting.Commands.Combat.Cmd<PERSON>ill, Grimoire", "Monster": "*"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdIndex, Grimoire", "Index": 7}, {"$type": "Grimoire.Botting.Commands.Quest.CmdCompleteQuest2, G<PERSON>oire", "QuestID": "[QuestID]", "ItemID": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "BrightoakRep"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdBlank3, G<PERSON>oire", "Text": "", "R": 220, "G": 220, "B": 220}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "4645"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdQuestAvailable, Grimoire", "Tag": "Quest", "Text": "Quest is available", "Value1": "4667", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "4667"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdPacket2, Grimoire", "Packet": "%xt%zm%equipItem%64634%32057%\n", "Delay": 1500}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdQuestNotInProgress, Grimoire", "Tag": "Quest", "Text": "Quest is not in progress", "Value1": "[QuestID]", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Quest.CmdAcceptQuest2, Grimoire", "QuestID": "[QuestID]"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdQuestCompleted, Grimoire", "Tag": "Quest", "Text": "Quest can be turned in", "Value1": "[QuestID]", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdIndex, Grimoire", "Type": 1, "Index": 7}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdMapIsNot, Grimoire", "Tag": "Map", "Text": "Map is not", "Value1": "Rivensylth", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Map.CmdJoin2, Grimoire", "Map": "Rivensylth", "Room": "[Lotebi Room.]", "Cell": "r4", "Pad": "Left"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdCellIsNot, Grimoire", "Tag": "Map", "Text": "Cell is not", "Value1": "o3", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Map.CmdMoveTo<PERSON>ell, Grimoire", "Cell": "o3", "Pad": "Left"}, {"$type": "Grimoire.Botting.Commands.Combat.Cmd<PERSON>ill, Grimoire", "Monster": "*"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdIndex, Grimoire", "Index": 7}, {"$type": "Grimoire.Botting.Commands.Quest.CmdCompleteQuest2, G<PERSON>oire", "QuestID": "[QuestID]", "ItemID": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "BrightoakRep"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdBlank3, G<PERSON>oire", "Text": "", "R": 220, "G": 220, "B": 220}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "4660"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdQuestAvailable, Grimoire", "Tag": "Quest", "Text": "Quest is available", "Value1": "4667", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "4667"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdPacket2, Grimoire", "Packet": "%xt%zm%equipItem%64634%32057%\n", "Delay": 1500}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdUpdateVar, Grimoire", "Tag": "Misc", "Text": "Update Variable", "Value1": "QuestID", "Value2": "4660"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdQuestNotInProgress, Grimoire", "Tag": "Quest", "Text": "Quest is not in progress", "Value1": "[QuestID]", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Quest.CmdAcceptQuest2, Grimoire", "QuestID": "[QuestID]"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdInTemp, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is in temp inventory", "Value1": "<PERSON>", "Value2": "3"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdIndex, Grimoire", "Type": 1, "Index": 7}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdMapIsNot, Grimoire", "Tag": "Map", "Text": "Map is not", "Value1": "brightoak", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Map.CmdJoin2, Grimoire", "Map": "brightoak", "Room": "[Lotebi Room.]", "Cell": "r2", "Pad": "Left"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdCellIsNot, Grimoire", "Tag": "Map", "Text": "Cell is not", "Value1": "r2", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Map.CmdMoveTo<PERSON>ell, Grimoire", "Cell": "r2", "Pad": "Left"}, {"$type": "Grimoire.Botting.Commands.Combat.Cmd<PERSON>ill, Grimoire", "Monster": "<PERSON><PERSON>"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdIndex, Grimoire", "Index": 7}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdInTemp, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is in temp inventory", "Value1": "<PERSON><PERSON>", "Value2": "4"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdIndex, Grimoire", "Type": 1, "Index": 7}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdMapIsNot, Grimoire", "Tag": "Map", "Text": "Map is not", "Value1": "brightoak", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Map.CmdJoin2, Grimoire", "Map": "brightoak", "Room": "[Lotebi Room.]", "Cell": "r2", "Pad": "Left"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdCellIsNot, Grimoire", "Tag": "Map", "Text": "Cell is not", "Value1": "r2", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Map.CmdMoveTo<PERSON>ell, Grimoire", "Cell": "r2", "Pad": "Left"}, {"$type": "Grimoire.Botting.Commands.Combat.Cmd<PERSON>ill, Grimoire", "Monster": "<PERSON><PERSON>"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdIndex, Grimoire", "Index": 7}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdInInventory, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is in inventory", "Value1": "Water of Life", "Value2": "1"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdIndex, Grimoire", "Type": 1, "Index": 10}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdMapIsNot, Grimoire", "Tag": "Map", "Text": "Map is not", "Value1": "Sandsea", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Map.CmdJoin2, Grimoire", "Map": "Sandsea", "Room": "[Lotebi Room.]", "Cell": "Inn", "Pad": "Right"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdCellIsNot, Grimoire", "Tag": "Map", "Text": "Cell is not", "Value1": "Inn", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Map.CmdMoveTo<PERSON>ell, Grimoire", "Cell": "Inn", "Pad": "Right"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdDelay, Grimoire", "Delay": 200}, {"$type": "Grimoire.Botting.Commands.Item.CmdLoad2, G<PERSON><PERSON>", "ShopId": "245"}, {"$type": "Grimoire.Botting.Commands.Item.CmdBuyFast, Grimoire", "ItemName": "Water of Life"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdDelay, Grimoire", "Delay": 200}, {"$type": "Grimoire.Botting.Commands.Misc.CmdIndex, Grimoire", "Index": 10}, {"$type": "Grimoire.Botting.Commands.Quest.CmdCompleteQuest2, G<PERSON>oire", "QuestID": "[QuestID]", "ItemID": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "BrightoakRep"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdBlank3, G<PERSON>oire", "Text": "", "R": 220, "G": 220, "B": 220}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "4661"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdQuestAvailable, Grimoire", "Tag": "Quest", "Text": "Quest is available", "Value1": "4667", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "4667"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdPacket2, Grimoire", "Packet": "%xt%zm%equipItem%64634%32057%\n", "Delay": 1500}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdQuestNotInProgress, Grimoire", "Tag": "Quest", "Text": "Quest is not in progress", "Value1": "[QuestID]", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Quest.CmdAcceptQuest2, Grimoire", "QuestID": "[QuestID]"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdInTemp, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is in temp inventory", "Value1": "Blighted Deer Cleared", "Value2": "5"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdIndex, Grimoire", "Type": 1, "Index": 7}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdMapIsNot, Grimoire", "Tag": "Map", "Text": "Map is not", "Value1": "<PERSON><PERSON><PERSON>", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Map.CmdJoin2, Grimoire", "Map": "<PERSON><PERSON><PERSON>", "Room": "[Lotebi Room.]", "Cell": "Enter", "Pad": "Spawn"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdCellIsNot, Grimoire", "Tag": "Map", "Text": "Cell is not", "Value1": "Enter", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Map.CmdMoveTo<PERSON>ell, Grimoire", "Cell": "Enter", "Pad": "Spawn"}, {"$type": "Grimoire.Botting.Commands.Combat.Cmd<PERSON>ill, Grimoire", "Monster": "*"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdIndex, Grimoire", "Index": 7}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdNotInInventory, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is not in inventory", "Value1": "Restoration of Nature Potion", "Value2": "1"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "4660"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdDelay, Grimoire", "Delay": 200}, {"$type": "Grimoire.Botting.Commands.Quest.CmdCompleteQuest2, G<PERSON>oire", "QuestID": "[QuestID]", "ItemID": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "BrightoakRep"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdBlank3, G<PERSON>oire", "Text": "", "R": 220, "G": 220, "B": 220}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "4662"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdQuestAvailable, Grimoire", "Tag": "Quest", "Text": "Quest is available", "Value1": "4667", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "4667"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdPacket2, Grimoire", "Packet": "%xt%zm%equipItem%64634%32057%\n", "Delay": 1500}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdQuestNotInProgress, Grimoire", "Tag": "Quest", "Text": "Quest is not in progress", "Value1": "[QuestID]", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Quest.CmdAcceptQuest2, Grimoire", "QuestID": "[QuestID]"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdInTemp, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is in temp inventory", "Value1": "<PERSON><PERSON> Defeated", "Value2": "12"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdIndex, Grimoire", "Type": 1, "Index": 7}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdMapIsNot, Grimoire", "Tag": "Map", "Text": "Map is not", "Value1": "<PERSON><PERSON><PERSON>", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Map.CmdJoin2, Grimoire", "Map": "<PERSON><PERSON><PERSON>", "Room": "[Lotebi Room.]", "Cell": "r4", "Pad": "Left"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdCellIsNot, Grimoire", "Tag": "Map", "Text": "Cell is not", "Value1": "r4", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Map.CmdMoveTo<PERSON>ell, Grimoire", "Cell": "r4", "Pad": "Left"}, {"$type": "Grimoire.Botting.Commands.Combat.Cmd<PERSON>ill, Grimoire", "Monster": "<PERSON><PERSON>"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdIndex, Grimoire", "Index": 7}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdNotInInventory, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is not in inventory", "Value1": "<PERSON><PERSON>'s <PERSON><PERSON><PERSON>", "Value2": "1"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "820"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdDelay, Grimoire", "Delay": 200}, {"$type": "Grimoire.Botting.Commands.Quest.CmdCompleteQuest2, G<PERSON>oire", "QuestID": "[QuestID]", "ItemID": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "BrightoakRep"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdBlank3, G<PERSON>oire", "Text": "", "R": 220, "G": 220, "B": 220}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "4663"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdQuestAvailable, Grimoire", "Tag": "Quest", "Text": "Quest is available", "Value1": "4667", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "4667"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdPacket2, Grimoire", "Packet": "%xt%zm%equipItem%64634%32057%\n", "Delay": 1500}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdQuestNotInProgress, Grimoire", "Tag": "Quest", "Text": "Quest is not in progress", "Value1": "[QuestID]", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Quest.CmdAcceptQuest2, Grimoire", "QuestID": "[QuestID]"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdInTemp, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is in temp inventory", "Value1": "<PERSON><PERSON><PERSON>", "Value2": "4"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdIndex, Grimoire", "Type": 1, "Index": 7}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdMapIsNot, Grimoire", "Tag": "Map", "Text": "Map is not", "Value1": "<PERSON><PERSON><PERSON>", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Map.CmdJoin2, Grimoire", "Map": "<PERSON><PERSON><PERSON>", "Room": "[Lotebi Room.]", "Cell": "r3", "Pad": "Left"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdCellIsNot, Grimoire", "Tag": "Map", "Text": "Cell is not", "Value1": "r3", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Map.CmdMoveTo<PERSON>ell, Grimoire", "Cell": "r3", "Pad": "Left"}, {"$type": "Grimoire.Botting.Commands.Combat.Cmd<PERSON>ill, Grimoire", "Monster": "Wolfrider"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdIndex, Grimoire", "Index": 7}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdNotInInventory, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is not in inventory", "Value1": "Restoration of Nature Potion", "Value2": "2"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "4660"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdDelay, Grimoire", "Delay": 200}, {"$type": "Grimoire.Botting.Commands.Quest.CmdCompleteQuest2, G<PERSON>oire", "QuestID": "[QuestID]", "ItemID": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "BrightoakRep"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdBlank3, G<PERSON>oire", "Text": "", "R": 220, "G": 220, "B": 220}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "4664"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdQuestAvailable, Grimoire", "Tag": "Quest", "Text": "Quest is available", "Value1": "4667", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "4667"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdPacket2, Grimoire", "Packet": "%xt%zm%equipItem%64634%32057%\n", "Delay": 1500}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdQuestNotInProgress, Grimoire", "Tag": "Quest", "Text": "Quest is not in progress", "Value1": "[QuestID]", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Quest.CmdAcceptQuest2, Grimoire", "QuestID": "[QuestID]"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdQuestCompleted, Grimoire", "Tag": "Quest", "Text": "Quest can be turned in", "Value1": "[QuestID]", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdIndex, Grimoire", "Type": 1, "Index": 7}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdMapIsNot, Grimoire", "Tag": "Map", "Text": "Map is not", "Value1": "<PERSON><PERSON><PERSON>", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Map.CmdJoin2, Grimoire", "Map": "<PERSON><PERSON><PERSON>", "Room": "[Lotebi Room.]", "Cell": "r5", "Pad": "Left"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdCellIsNot, Grimoire", "Tag": "Map", "Text": "Cell is not", "Value1": "r5", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Map.CmdMoveTo<PERSON>ell, Grimoire", "Cell": "r5", "Pad": "Left"}, {"$type": "Grimoire.Botting.Commands.Combat.Cmd<PERSON>ill, Grimoire", "Monster": "Ratawampus"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdIndex, Grimoire", "Index": 7}, {"$type": "Grimoire.Botting.Commands.Quest.CmdCompleteQuest2, G<PERSON>oire", "QuestID": "[QuestID]", "ItemID": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "BrightoakRep"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdBlank3, G<PERSON>oire", "Text": "", "R": 220, "G": 220, "B": 220}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "4665"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdQuestAvailable, Grimoire", "Tag": "Quest", "Text": "Quest is available", "Value1": "4667", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "4667"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdPacket2, Grimoire", "Packet": "%xt%zm%equipItem%64634%32057%\n", "Delay": 1500}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdQuestNotInProgress, Grimoire", "Tag": "Quest", "Text": "Quest is not in progress", "Value1": "[QuestID]", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Quest.CmdAcceptQuest2, Grimoire", "QuestID": "[QuestID]"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdInTemp, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is in temp inventory", "Value1": "Ratawampus Cleared", "Value2": "2"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdIndex, Grimoire", "Type": 1, "Index": 7}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdMapIsNot, Grimoire", "Tag": "Map", "Text": "Map is not", "Value1": "<PERSON><PERSON><PERSON>", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Map.CmdJoin2, Grimoire", "Map": "<PERSON><PERSON><PERSON>", "Room": "[Lotebi Room.]", "Cell": "r4", "Pad": "Bottom"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdCellIsNot, Grimoire", "Tag": "Map", "Text": "Cell is not", "Value1": "r4", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Map.CmdMoveTo<PERSON>ell, Grimoire", "Cell": "r4", "Pad": "Bottom"}, {"$type": "Grimoire.Botting.Commands.Combat.Cmd<PERSON>ill, Grimoire", "Monster": "Ratawampus"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdIndex, Grimoire", "Index": 7}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdInTemp, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is in temp inventory", "Value1": "<PERSON><PERSON> Cleared", "Value2": "3"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdIndex, Grimoire", "Type": 1, "Index": 7}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdMapIsNot, Grimoire", "Tag": "Map", "Text": "Map is not", "Value1": "<PERSON><PERSON><PERSON>", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Map.CmdJoin2, Grimoire", "Map": "<PERSON><PERSON><PERSON>", "Room": "[Lotebi Room.]", "Cell": "r4", "Pad": "Bottom"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdCellIsNot, Grimoire", "Tag": "Map", "Text": "Cell is not", "Value1": "r4", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Map.CmdMoveTo<PERSON>ell, Grimoire", "Cell": "r4", "Pad": "Bottom"}, {"$type": "Grimoire.Botting.Commands.Combat.Cmd<PERSON>ill, Grimoire", "Monster": "<PERSON><PERSON>"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdIndex, Grimoire", "Index": 7}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdNotInInventory, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is not in inventory", "Value1": "Restoration of Nature Potion", "Value2": "3"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "4660"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdDelay, Grimoire", "Delay": 200}, {"$type": "Grimoire.Botting.Commands.Quest.CmdCompleteQuest2, G<PERSON>oire", "QuestID": "[QuestID]", "ItemID": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "BrightoakRep"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdBlank3, G<PERSON>oire", "Text": "", "R": 220, "G": 220, "B": 220}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "4666"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdQuestAvailable, Grimoire", "Tag": "Quest", "Text": "Quest is available", "Value1": "4667", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "4667"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdPacket2, Grimoire", "Packet": "%xt%zm%equipItem%64634%32057%\n", "Delay": 1500}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdQuestNotInProgress, Grimoire", "Tag": "Quest", "Text": "Quest is not in progress", "Value1": "[QuestID]", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Quest.CmdAcceptQuest2, Grimoire", "QuestID": "[QuestID]"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdInTemp, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is in temp inventory", "Value1": "<PERSON><PERSON> Contained", "Value2": "6"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdIndex, Grimoire", "Type": 1, "Index": 7}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdMapIsNot, Grimoire", "Tag": "Map", "Text": "Map is not", "Value1": "<PERSON><PERSON><PERSON>", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Map.CmdJoin2, Grimoire", "Map": "<PERSON><PERSON><PERSON>", "Room": "[Lotebi Room.]", "Cell": "r2", "Pad": "Bottom"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdCellIsNot, Grimoire", "Tag": "Map", "Text": "Cell is not", "Value1": "r2", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Map.CmdMoveTo<PERSON>ell, Grimoire", "Cell": "r2", "Pad": "Bottom"}, {"$type": "Grimoire.Botting.Commands.Combat.Cmd<PERSON>ill, Grimoire", "Monster": "<PERSON><PERSON>"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdIndex, Grimoire", "Index": 7}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdNotInInventory, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is not in inventory", "Value1": "Restoration of Nature Potion", "Value2": "4"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "4660"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdDelay, Grimoire", "Delay": 200}, {"$type": "Grimoire.Botting.Commands.Quest.CmdCompleteQuest2, G<PERSON>oire", "QuestID": "[QuestID]", "ItemID": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "BrightoakRep"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdBlank3, G<PERSON>oire", "Text": "", "R": 220, "G": 220, "B": 220}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "820"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdUpdateVar, Grimoire", "Tag": "Misc", "Text": "Update Variable", "Value1": "QuestID", "Value2": "820"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdQuestNotInProgress, Grimoire", "Tag": "Quest", "Text": "Quest is not in progress", "Value1": "[QuestID]", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Quest.CmdAcceptQuest2, Grimoire", "QuestID": "[QuestID]"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdInTemp, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is in temp inventory", "Value1": "<PERSON>iggly <PERSON>", "Value2": "15"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdIndex, Grimoire", "Type": 1, "Index": 7}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdMapIsNot, Grimoire", "Tag": "Map", "Text": "Map is not", "Value1": "cloister", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Map.CmdJoin2, Grimoire", "Map": "cloister", "Room": "[Lotebi Room.]", "Cell": "r5", "Pad": "Left"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdCellIsNot, Grimoire", "Tag": "Map", "Text": "Cell is not", "Value1": "r5", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Map.CmdMoveTo<PERSON>ell, Grimoire", "Cell": "r5", "Pad": "Left"}, {"$type": "Grimoire.Botting.Commands.Combat.Cmd<PERSON>ill, Grimoire", "Monster": "*"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdIndex, Grimoire", "Index": 7}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdInTemp, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is in temp inventory", "Value1": "MegaMite", "Value2": "10"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdIndex, Grimoire", "Type": 1, "Index": 7}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdMapIsNot, Grimoire", "Tag": "Map", "Text": "Map is not", "Value1": "cloister", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Map.CmdJoin2, Grimoire", "Map": "cloister", "Room": "[Lotebi Room.]", "Cell": "r4", "Pad": "Right"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdCellIsNot, Grimoire", "Tag": "Map", "Text": "Cell is not", "Value1": "r4", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Map.CmdMoveTo<PERSON>ell, Grimoire", "Cell": "r4", "Pad": "Right"}, {"$type": "Grimoire.Botting.Commands.Combat.Cmd<PERSON>ill, Grimoire", "Monster": "<PERSON><PERSON>"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdIndex, Grimoire", "Index": 7}, {"$type": "Grimoire.Botting.Commands.Misc.CmdDelay, Grimoire", "Delay": 200}, {"$type": "Grimoire.Botting.Commands.Quest.CmdCompleteQuest2, G<PERSON>oire", "QuestID": "[QuestID]", "ItemID": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "4662"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdBlank3, G<PERSON>oire", "Text": "", "R": 220, "G": 220, "B": 220}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "4667"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdUpdateVar, Grimoire", "Tag": "Misc", "Text": "Update Variable", "Value1": "QuestID", "Value2": "4667"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdQuestNotInProgress, Grimoire", "Tag": "Quest", "Text": "Quest is not in progress", "Value1": "[QuestID]", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Quest.CmdAcceptQuest2, Grimoire", "QuestID": "[QuestID]"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdQuestCompleted, Grimoire", "Tag": "Quest", "Text": "Quest can be turned in", "Value1": "[QuestID]", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdIndex, Grimoire", "Type": 1, "Index": 5}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdMapIsNot, Grimoire", "Tag": "Map", "Text": "Map is not", "Value1": "elf<PERSON>", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Map.CmdJoin2, Grimoire", "Map": "elf<PERSON>", "Room": "[Lotebi Room.]", "Cell": "Enter", "Pad": "Spawn"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdPacket2, Grimoire", "Packet": "%xt%zm%getMapItem%146625%3984%\n", "Delay": 500}, {"$type": "Grimoire.Botting.Commands.Misc.CmdIndex, Grimoire", "Index": 5}, {"$type": "Grimoire.Botting.Commands.Quest.CmdCompleteQuest2, G<PERSON>oire", "QuestID": "[QuestID]", "ItemID": ""}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdFactionRankLessThan, Grimoire", "Tag": "This player", "Text": "Faction Rank is less than", "Value1": "Brightoak", "Value2": "10"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "4667"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "CHECK"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdBlank3, G<PERSON>oire", "Text": "", "R": 220, "G": 220, "B": 220}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "KILLT"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdMapIsNot, Grimoire", "Tag": "Map", "Text": "Map is not", "Value1": "[MapName]", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Map.CmdJoin2, Grimoire", "Map": "[MapName]", "Room": "[Lotebi Room.]", "Cell": "[Cell1]", "Pad": "[Cell2]"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdCellIsNot, Grimoire", "Tag": "Map", "Text": "Cell is not", "Value1": "[Cell1]", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Map.CmdMoveTo<PERSON>ell, Grimoire", "Cell": "[Cell1]", "Pad": "[Cell2]"}, {"$type": "Grimoire.Botting.Commands.Combat.Cmd<PERSON>ill, Grimoire", "Monster": "[<PERSON><PERSON><PERSON>]"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdNotInTemp, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is not in temp", "Value1": "[DropName]", "Value2": "[DropCount]"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "KILLT"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "CHECK"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdBlank3, G<PERSON>oire", "Text": "", "R": 220, "G": 220, "B": 220}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "KILL"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdMapIsNot, Grimoire", "Tag": "Map", "Text": "Map is not", "Value1": "[MapName]", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Map.CmdJoin2, Grimoire", "Map": "[MapName]", "Room": "[Lotebi Room.]", "Cell": "[Cell1]", "Pad": "[Cell2]"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdCellIsNot, Grimoire", "Tag": "Map", "Text": "Cell is not", "Value1": "[Cell1]", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Map.CmdMoveTo<PERSON>ell, Grimoire", "Cell": "[Cell1]", "Pad": "[Cell2]"}, {"$type": "Grimoire.Botting.Commands.Combat.Cmd<PERSON>ill, Grimoire", "Monster": "[<PERSON><PERSON><PERSON>]"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdNotInInventory, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is not in inventory", "Value1": "[DropName]", "Value2": "[DropCount]"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "KILL"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "CHECK"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdBlank3, G<PERSON>oire", "Text": "", "R": 220, "G": 220, "B": 220}, {"$type": "Grimoire.Botting.Commands.Misc.CmdBlank2, G<PERSON>oire", "Text": "[250,250,250]Congratulations!"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "END"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdMapIsNot, Grimoire", "Tag": "Map", "Text": "Map is not", "Value1": "Wikipic", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Map.CmdJoin2, Grimoire", "Map": "Wikipic", "Room": "[Lotebi Room.]", "Cell": "Enter", "Pad": "Spawn"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdMapIsNot, Grimoire", "Tag": "Map", "Text": "Map is not", "Value1": "Wikipic", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdIndex, Grimoire", "Index": 2}, {"$type": "Grimoire.Botting.Commands.Misc.CmdDelay, Grimoire", "Delay": 500}, {"$type": "Grimoire.Botting.Commands.Misc.CmdPacket, Grimoire", "Packet": "%xt%chatm%69061%zone~This Quest's bot is made by Lotebi.%Lotebi Bot%25810%69061%0%", "Client": true}, {"$type": "Grimoire.Botting.Commands.Misc.CmdPacket, Grimoire", "Packet": "%xt%chatm%69061%zone~Finished Quest's..%Lotebi Bot%25810%69061%0%", "Client": true}, {"$type": "Grimoire.Botting.Commands.Misc.CmdReturn, Grimoire"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdStopBotWithMessage, Grimoire", "Message": "Successfully Quest's!"}]}, "Skills": {"$type": "System.Collections.Generic.List`1[[Grimoire.Game.Data.Skill, Grimoire]], mscorlib", "$values": []}, "Quests": {"$type": "System.Collections.Generic.List`1[[Grimoire.Game.Data.Quest, Grimoire]], mscorlib", "$values": [{"$type": "Grimoire.Game.Data.Quest, Grimoire", "QuestID": 800, "Text": "800", "IsInProgress": true}, {"$type": "Grimoire.Game.Data.Quest, Grimoire", "QuestID": 798, "Text": "798", "IsInProgress": true}, {"$type": "Grimoire.Game.Data.Quest, Grimoire", "QuestID": 794, "Text": "794", "IsInProgress": true}]}, "Author": "<PERSON><PERSON><PERSON>", "Description": "Youtube.com/Lotebi\r\nDiscord.io/Lotebi", "Boosts": {"$type": "System.Collections.Generic.List`1[[Grimoire.Game.Data.InventoryItem, Grimoire]], mscorlib", "$values": []}, "Drops": {"$type": "System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib", "$values": ["Bot By: <PERSON><PERSON><PERSON>", "Youtube.com/Lotebi", "Discord.io/LOTEBI", "l", "Discord.io/LOTEBI", "Discord.io/LOTEBI", "Stonewrit Found!", "Handle Found!", "Hilt Found!", "Legendary Stonewrit", "Legendary <PERSON>", "Legendary <PERSON>", "Restoration of Nature Potion", "Water of Life", "<PERSON><PERSON>'s <PERSON><PERSON><PERSON>"]}, "Server": {"$type": "Grimoire.Game.Data.Server, Grimoire", "iMax": 1000, "sName": "Twilly", "sLang": "xx", "iPort": 5588, "sIP": "twilly.aqw.aq.com"}, "SkillDelay": 100, "EnablePickup": true, "EnableRejection": true, "AutoRelogin": true, "RelogDelay": 5000, "BotDelay": 10, "SkipDelayIndexIf": true, "InfiniteAttackRange": true, "Untarget": true, "HidePlayers": true, "SkipCutscenes": true, "DisableAnimations": true, "AutoSaveState": true, "WalkSpeed": 8, "NotifyUponDrop": {"$type": "System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib", "$values": []}, "RestMp": 60, "RestHp": 60, "RestartUponDeath": true, "Items": {"$type": "System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib", "$values": ["Youtube.com/Lotebi", "discord.io/Lotebi", "|"]}, "RelogUponQuestFailure": true, "RelogUponItemBuyFailure": true, "AFK": true}