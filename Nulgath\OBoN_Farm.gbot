{"$type": "Grimoire.Botting.Configuration, Grimoire", "Commands": {"$type": "System.Collections.Generic.List`1[[Grimoire.Botting.IBotCommand, Grimoire]], mscorlib", "$values": [{"$type": "Grimoire.Botting.Commands.Map.CmdJ<PERSON>n, Grimoire", "Map": "nulgath-9099", "Cell": "End", "Pad": "Left"}, {"$type": "Grimoire.Botting.Commands.Quest.CmdAcceptQuest, Grimoire", "Quest": {"$type": "Grimoire.Game.Data.Quest, Grimoire", "QuestID": 2857}}, {"$type": "Grimoire.Botting.Commands.Quest.CmdAcceptQuest, Grimoire", "Quest": {"$type": "Grimoire.Game.Data.Quest, Grimoire", "QuestID": 599}}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdInInventory, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is in inventory", "Value1": "Escherion's <PERSON><PERSON>", "Value2": "*"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "QUEST"}, {"$type": "Grimoire.Botting.Commands.Combat.CmdKill<PERSON>or, Grimoire", "Monster": "*", "ItemName": "Tainted Soul", "Quantity": "5", "AfterKills": 1, "DelayAfterKill": 500}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "QUEST"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdCellIsNot, Grimoire", "Tag": "Map", "Text": "Cell is not", "Value1": "Blank", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Map.CmdMoveTo<PERSON>ell, Grimoire", "Cell": "Blank", "Pad": "Spawn"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdDelay, Grimoire", "Delay": 500}, {"$type": "Grimoire.Botting.Commands.Quest.CmdCompleteQuest, Grimoire", "Quest": {"$type": "Grimoire.Game.Data.Quest, Grimoire", "QuestID": 599}, "CompleteTry": 1}, {"$type": "Grimoire.Botting.Commands.Misc.CmdDelay, Grimoire", "Delay": 1000}, {"$type": "Grimoire.Botting.Commands.Quest.CmdCompleteQuest, Grimoire", "Quest": {"$type": "Grimoire.Game.Data.Quest, Grimoire", "QuestID": 2857}, "CompleteTry": 1}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdInInventory, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is in inventory", "Value1": "Escherion's <PERSON><PERSON>", "Value2": "1"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdIndex, Grimoire", "Index": 2}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdInInventory, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is in inventory", "Value1": "Tainted Soul", "Value2": "1"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "QUEST"}]}, "Skills": {"$type": "System.Collections.Generic.List`1[[Grimoire.Game.Data.Skill, Grimoire]], mscorlib", "$values": [{"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "1: Atramentous Shade", "Index": "1"}, {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "2: <PERSON> Purgatory", "Index": "2"}, {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "3: Depraved Empowerment", "Index": "3"}, {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "4: <PERSON><PERSON><PERSON>", "Index": "4"}]}, "Quests": {"$type": "System.Collections.Generic.List`1[[Grimoire.Game.Data.Quest, Grimoire]], mscorlib", "$values": []}, "Author": "Author", "Description": "Description", "Boosts": {"$type": "System.Collections.Generic.List`1[[Grimoire.Game.Data.InventoryItem, Grimoire]], mscorlib", "$values": []}, "Drops": {"$type": "System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib", "$values": ["Gem of Nulgath", "Diamond of Nulgath", "Voucher of Nulgath", "Voucher of Nulgath (non-mem)", "<PERSON><PERSON>", "Dark Crystal Shard", "Totem of Nulgath", "Blood Gem of the Archfiend", "Unidentified 13", "Tainted Gem", "Essence of Nulgath", "Slugfit Horn", "Tainted Soul", "Escherion's <PERSON><PERSON>", "Escherion's <PERSON><PERSON>"]}, "Server": {"$type": "Grimoire.Game.Data.Server, Grimoire", "sName": "<PERSON><PERSON>", "iPort": 41957}, "ExitCombatBeforeRest": true, "ExitCombatBeforeQuest": true, "EnablePickup": true, "EnableRejection": true, "AutoRelogin": true, "RelogDelay": 5000, "BotDelay": 1000, "SkipDelayIndexIf": true, "InfiniteAttackRange": true, "DisableAnimations": true, "WalkSpeed": 8, "NotifyUponDrop": {"$type": "System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib", "$values": []}, "RestMp": 60, "RestHp": 60, "RestartUponDeath": true}