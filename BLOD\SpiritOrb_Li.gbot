{"$type": "Grimoire.Botting.Configuration, Grimoire", "Commands": {"$type": "System.Collections.Generic.List`1[[Grimoire.Botting.IBotCommand, Grimoire]], mscorlib", "$values": [{"$type": "Grimoire.Botting.Commands.Map.CmdJ<PERSON>n, Grimoire", "Map": "battleunderb", "Cell": "Enter", "Pad": "Spawn"}, {"$type": "Grimoire.Botting.Commands.Combat.CmdKill<PERSON>or, Grimoire", "Monster": "*", "ItemName": "Bone Dust,Undead Energy,Undead Essence", "Quantity": "1000,1000,1000", "IsGetDrops": true, "AfterKills": 5, "DelayAfterKill": 200}, {"$type": "Grimoire.Botting.Commands.Map.CmdMoveTo<PERSON>ell, Grimoire", "Cell": "Blank", "Pad": "Spawn"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdDelay, Grimoire", "Delay": 500}, {"$type": "Grimoire.Botting.Commands.Map.CmdJ<PERSON>n, Grimoire", "Map": "necropolis", "Cell": "Enter", "Pad": "Spawn"}, {"$type": "Grimoire.Botting.Commands.Quest.CmdAcceptQuest, Grimoire", "Quest": {"$type": "Grimoire.Game.Data.Quest, Grimoire", "QuestID": 2082, "Text": ""}}, {"$type": "Grimoire.Botting.Commands.Quest.CmdCompleteQuest, Grimoire", "Quest": {"$type": "Grimoire.Game.Data.Quest, Grimoire", "QuestID": 2082, "Text": ""}, "CompleteTry": 40, "Delay": 1000}, {"$type": "Grimoire.Botting.Commands.Quest.CmdAcceptQuest, Grimoire", "Quest": {"$type": "Grimoire.Game.Data.Quest, Grimoire", "QuestID": 2083, "Text": ""}}, {"$type": "Grimoire.Botting.Commands.Quest.CmdCompleteQuest, Grimoire", "Quest": {"$type": "Grimoire.Game.Data.Quest, Grimoire", "QuestID": 2083, "Text": ""}, "CompleteTry": 100, "Delay": 1000}, {"$type": "Grimoire.Botting.Commands.Misc.CmdPacket, Grimoire", "Packet": "%xt%zm%buyItem%106012%12184%422%1181%", "SpamTimes": 20, "Delay": 1000}]}, "Skills": {"$type": "System.Collections.Generic.List`1[[Grimoire.Game.Data.Skill, Grimoire]], mscorlib", "$values": [{"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "2: Hydrophobia", "Index": "2"}, {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "1: <PERSON><PERSON><PERSON>'s Flame", "Index": "1"}, {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "[S] 3: Stygian Pact", "Index": "3", "Type": 1}, {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "3: Stygian Pact", "Index": "3"}]}, "Quests": {"$type": "System.Collections.Generic.List`1[[Grimoire.Game.Data.Quest, Grimoire]], mscorlib", "$values": []}, "Author": "Author", "Description": "Description", "Boosts": {"$type": "System.Collections.Generic.List`1[[Grimoire.Game.Data.InventoryItem, Grimoire]], mscorlib", "$values": []}, "Drops": {"$type": "System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib", "$values": ["bone dust", "undead energy", "undead essence", "spirit orb"]}, "Server": {"$type": "Grimoire.Game.Data.Server, Grimoire", "sName": "<PERSON><PERSON><PERSON>", "iPort": 46030}, "ExitCombatBeforeRest": true, "DropDelay": 1000, "EnablePickup": true, "AutoRelogin": true, "RelogDelay": 5000, "BotDelay": 1000, "SkipDelayIndexIf": true, "InfiniteAttackRange": true, "ProvokeMonsters": true, "DisableAnimations": true, "WalkSpeed": 8, "NotifyUponDrop": {"$type": "System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib", "$values": []}, "RestMp": 60, "RestHp": 60, "RestartUponDeath": true}