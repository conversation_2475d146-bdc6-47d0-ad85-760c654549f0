{"$type": "Grimoire.Botting.Configuration, Grimoire", "Commands": {"$type": "System.Collections.Generic.List`1[[Grimoire.Botting.IBotCommand, Grimoire]], mscorlib", "$values": [{"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdSetVar, Grimoire", "Tag": "Misc", "Text": "Set Temporary Variable", "Value1": "PLAYER", "Value2": "Remake"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdMapIs, Grimoire", "Tag": "Map", "Text": "Map is", "Value1": "<PERSON><PERSON><PERSON><PERSON>", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "S"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdBlank3, G<PERSON>oire", "Text": "...", "Alpha": 1, "R": 220, "G": 220, "B": 220}, {"$type": "Grimoire.Botting.Commands.Misc.CmdPacket, Grimoire", "Packet": "{\"t\":\"xt\",\"b\":{\"r\":-1,\"o\":{\"cmd\":\"updateQuest\",\"iValue\":30,\"iIndex\":182}}}", "SpamTimes": 1, "Delay": 1000, "ForClient": true}, {"$type": "Grimoire.Botting.Commands.Map.CmdJ<PERSON>n, Grimoire", "Map": "<PERSON><PERSON><PERSON>h-9099", "Cell": "Enter", "Pad": "Spawn"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdBlank3, G<PERSON>oire", "Text": "...", "Alpha": 1, "R": 220, "G": 220, "B": 220}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "S"}, {"$type": "Grimoire.Botting.Commands.Combat.CmdAttack, Grimoire", "Monster": "*"}, {"$type": "Grimoire.Botting.Commands.Combat.CmdUseSkill, Grimoire", "Skill": {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "Lingering Light", "Index": "1"}, "Targeted": true}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdHealthLessThan, Grimoire", "Tag": "This player", "Text": "Health is less than", "Value1": "2800", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Combat.CmdUseSkill, Grimoire", "Skill": {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "Wicked Purgatory", "Index": "2"}}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdPlayersHPLessThan, Grimoire", "Tag": "Player", "Text": "Health is less than", "Value1": "[PLAYER]", "Value2": "80"}, {"$type": "Grimoire.Botting.Commands.Combat.CmdUseSkill, Grimoire", "Skill": {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "Wicked Purgatory", "Index": "2"}}, {"$type": "Grimoire.Botting.Commands.Combat.CmdUseSkill, Grimoire", "Skill": {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "Light Blast", "Index": "3"}, "Targeted": true}, {"$type": "Grimoire.Botting.Commands.Combat.CmdUseSkill, Grimoire", "Skill": {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "Blessing of Order", "Index": "4"}, "Targeted": true}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "S"}]}, "Skills": {"$type": "System.Collections.Generic.List`1[[Grimoire.Game.Data.Skill, Grimoire]], mscorlib", "$values": []}, "Quests": {"$type": "System.Collections.Generic.List`1[[Grimoire.Game.Data.Quest, Grimoire]], mscorlib", "$values": []}, "Author": "", "Description": "\r\n", "Boosts": {"$type": "System.Collections.Generic.List`1[[Grimoire.Game.Data.InventoryItem, Grimoire]], mscorlib", "$values": []}, "Drops": {"$type": "System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib", "$values": []}, "Server": {"$type": "Grimoire.Game.Data.Server, Grimoire", "sName": "Twilly", "iPort": 5588}, "ExitCombatBeforeRest": true, "ExitCombatBeforeQuest": true, "EnablePickup": true, "RelogDelay": 5000, "SkipDelayIndexIf": true, "InfiniteAttackRange": true, "DisableAnimations": true, "WalkSpeed": 8, "NotifyUponDrop": {"$type": "System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib", "$values": []}, "RestMp": 60, "RestHp": 60, "RestartUponDeath": true, "Items": {"$type": "System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib", "$values": []}, "DropDelay": 500, "FollowCheck": true, "FollowName": "remake"}