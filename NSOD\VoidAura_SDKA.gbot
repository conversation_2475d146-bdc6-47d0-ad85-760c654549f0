{"$type": "Grimoire.Botting.Configuration, Grimoire", "Commands": {"$type": "System.Collections.Generic.List`1[[Grimoire.Botting.IBotCommand, Grimoire]], mscorlib", "$values": [{"$type": "Grimoire.Botting.Commands.Map.CmdJ<PERSON>n, Grimoire", "Map": "shadowrealmpast", "Cell": "Enter", "Pad": "Spawn"}, {"$type": "Grimoire.Botting.Commands.Combat.CmdKill<PERSON>or, Grimoire", "Monster": "*", "ItemName": "Empowered <PERSON><PERSON><PERSON>", "Quantity": "50", "KillPriority": "", "IsGetDrops": true, "AfterKills": 3, "DelayAfterKill": 500}, {"$type": "Grimoire.Botting.Commands.Map.CmdMoveTo<PERSON>ell, Grimoire", "Cell": "r4", "Pad": "Left"}, {"$type": "Grimoire.Botting.Commands.Combat.CmdKill<PERSON>or, Grimoire", "Monster": "*", "ItemName": "<PERSON><PERSON>", "Quantity": "3", "KillPriority": "", "AfterKills": 3, "DelayAfterKill": 500}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLog, Grimoire", "Text": "[{TIME: 24}] Void Aura :  {ITEM: Void Aura}"}]}, "Skills": {"$type": "System.Collections.Generic.List`1[[Grimoire.Game.Data.Skill, Grimoire]], mscorlib", "$values": [{"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "3: Depraved Empowerment", "Index": "3"}, {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "4: <PERSON> Scourge", "Index": "4"}, {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "2: Onyx Combustion", "Index": "2"}, {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "1: Overshadowed", "Index": "1"}]}, "Quests": {"$type": "System.Collections.Generic.List`1[[Grimoire.Game.Data.Quest, Grimoire]], mscorlib", "$values": [{"$type": "Grimoire.Game.Data.Quest, Grimoire", "QuestID": 4439, "CompleteInBlank": true, "Text": "4439 [InBlank]"}]}, "Author": "", "Description": "{\\rtf1\\ansi\\deff0{\\fonttbl{\\f0\\fnil\\fcharset0 Microsoft Sans Serif;}}\r\n{\\colortbl ;\\red220\\green220\\blue220;}\r\n\\viewkind4\\uc1\\pard\\cf1\\lang2057\\b\\f0\\fs29 Description\\par\r\n}\r\n", "Boosts": {"$type": "System.Collections.Generic.List`1[[Grimoire.Game.Data.InventoryItem, Grimoire]], mscorlib", "$values": []}, "Drops": {"$type": "System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib", "$values": ["Empowered <PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Void Aura"]}, "Server": {"$type": "Grimoire.Game.Data.Server, Grimoire", "sName": "Artix", "iPort": 36144}, "ExitCombatBeforeRest": true, "ExitCombatBeforeQuest": true, "EnablePickup": true, "EnableRejection": true, "AutoRelogin": true, "RelogDelay": 5000, "BotDelay": 1000, "SkipDelayIndexIf": true, "InfiniteAttackRange": true, "ProvokeMonsters": true, "LagKiller": true, "DisableAnimations": true, "WalkSpeed": 8, "NotifyUponDrop": {"$type": "System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib", "$values": []}, "RestMp": 60, "RestHp": 60, "RestartUponDeath": true, "Items": {"$type": "System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib", "$values": []}, "DropDelay": 1000}