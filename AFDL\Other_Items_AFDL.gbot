{"$type": "Grimoire.Botting.Configuration, Grimoire", "Commands": {"$type": "System.Collections.Generic.List`1[[Grimoire.Botting.IBotCommand, Grimoire]], mscorlib", "$values": [{"$type": "Grimoire.Botting.Commands.Map.CmdMoveTo<PERSON>ell, Grimoire", "Cell": "Blank", "Pad": "Spawn"}, {"$type": "Grimoire.Botting.Commands.Quest.CmdAcceptQuest, Grimoire", "Quest": {"$type": "Grimoire.Game.Data.Quest, Grimoire", "QuestID": 5260, "Text": ""}}, {"$type": "Grimoire.Botting.Commands.Combat.CmdShortHunt, Grimoire", "Map": "starsinc", "Cell": "r4", "Pad": "Top", "Monster": "Living Star", "ItemName": "Living Star Essence", "Quantity": "100", "IsGetDrops": true, "AfterKills": 5, "DelayAfterKill": 500, "BlankFirst": true}, {"$type": "Grimoire.Botting.Commands.Combat.CmdShortHunt, Grimoire", "Map": "alliance", "Cell": "r3", "Pad": "Down", "Monster": "*", "ItemName": "Chaorruption Essence", "Quantity": "75", "IsGetDrops": true, "AfterKills": 5, "DelayAfterKill": 500, "BlankFirst": true}, {"$type": "Grimoire.Botting.Commands.Combat.CmdShortHunt, Grimoire", "Map": "underworld", "Cell": "boss1", "Pad": "Left", "Monster": "*", "ItemName": "Essence of Klunk", "Quantity": "1", "AfterKills": 1, "DelayAfterKill": 500, "BlankFirst": true}, {"$type": "Grimoire.Botting.Commands.Map.CmdMoveTo<PERSON>ell, Grimoire", "Cell": "Blank", "Pad": "Spawn"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdNotInInventory, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is not in inventory", "Value1": "Undead Essence", "Value2": "1000"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "UE"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdNotInInventory, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is not in inventory", "Value1": "Archfiend's Favor", "Value2": "5000"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "AF"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdNotInInventory, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is not in inventory", "Value1": "Essence of Nulgath", "Value2": "100"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "EON"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdPing, Grimoire"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdStop, Grimoire"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "AF"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdProvoke, Grimoire", "Set": true}, {"$type": "Grimoire.Botting.Commands.Map.CmdJ<PERSON>n, Grimoire", "Map": "<PERSON><PERSON><PERSON><PERSON>", "Cell": "r2", "Pad": "Up"}, {"$type": "Grimoire.Botting.Commands.Combat.CmdKill<PERSON>or, Grimoire", "Monster": "*", "ItemName": "Archfiend's <PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>'s <PERSON><PERSON><PERSON><PERSON>", "Quantity": "5000,1000", "IsGetDrops": true, "AfterKills": 5, "DelayAfterKill": 500}, {"$type": "Grimoire.Botting.Commands.Misc.CmdProvoke, Grimoire"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdRestart, Grimoire"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "UE"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdProvoke, Grimoire", "Set": true}, {"$type": "Grimoire.Botting.Commands.Map.CmdJ<PERSON>n, Grimoire", "Map": "battleunderb", "Cell": "Enter", "Pad": "Spawn"}, {"$type": "Grimoire.Botting.Commands.Combat.CmdKill<PERSON>or, Grimoire", "Monster": "*", "ItemName": "Undead Essence,Bone Dust", "Quantity": "1000,10", "IsGetDrops": true, "AfterKills": 5, "DelayAfterKill": 500}, {"$type": "Grimoire.Botting.Commands.Misc.CmdProvoke, Grimoire"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdRestart, Grimoire"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "EON"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdMapIsNot, Grimoire", "Tag": "Map", "Text": "Map is not", "Value1": "terc<PERSON><PERSON><PERSON><PERSON>", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Map.CmdJ<PERSON>n, Grimoire", "Map": "citadel-a", "Cell": "m22", "Pad": "Left"}, {"$type": "Grimoire.Botting.Commands.Map.CmdJ<PERSON>n, Grimoire", "Map": "terc<PERSON><PERSON><PERSON><PERSON>", "Cell": "m2", "Pad": "Left"}, {"$type": "Grimoire.Botting.Commands.Combat.CmdKill<PERSON>or, Grimoire", "Monster": "*", "ItemName": "Essence of Nulgath", "Quantity": "100", "AfterKills": 1, "DelayAfterKill": 500}, {"$type": "Grimoire.Botting.Commands.Misc.CmdRestart, Grimoire"}]}, "Skills": {"$type": "System.Collections.Generic.List`1[[Grimoire.Game.Data.Skill, Grimoire]], mscorlib", "$values": [{"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "1: Atramentous Shade", "Index": "1"}, {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "2: <PERSON> Purgatory", "Index": "2"}, {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "3: Depraved Empowerment", "Index": "3"}, {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "4: <PERSON><PERSON><PERSON>", "Index": "4"}]}, "Quests": {"$type": "System.Collections.Generic.List`1[[Grimoire.Game.Data.Quest, Grimoire]], mscorlib", "$values": []}, "Author": "Author", "Description": "Description", "Boosts": {"$type": "System.Collections.Generic.List`1[[Grimoire.Game.Data.InventoryItem, Grimoire]], mscorlib", "$values": []}, "Drops": {"$type": "System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib", "$values": ["Undead Essence", "Bone Dust", "Living Star Essence", "Chaorruption Essence", "Essence of Klunk", "Essence of Nulgath"]}, "Server": {"$type": "Grimoire.Game.Data.Server, Grimoire", "sName": "Artix", "iPort": 46604}, "SkillDelay": 100, "ExitCombatBeforeRest": true, "ExitCombatBeforeQuest": true, "DropDelay": 1000, "EnablePickup": true, "EnableRejection": true, "AutoRelogin": true, "RelogDelay": 5000, "RelogRetryUponFailure": true, "BotDelay": 1000, "SkipDelayIndexIf": true, "InfiniteAttackRange": true, "DisableAnimations": true, "WalkSpeed": 8, "NotifyUponDrop": {"$type": "System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib", "$values": []}, "RestMp": 60, "RestHp": 60, "RestartUponDeath": true}