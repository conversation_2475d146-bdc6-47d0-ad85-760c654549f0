{"$type": "Grimoire.Botting.Configuration, Grimoire", "Commands": {"$type": "System.Collections.Generic.List`1[[Grimoire.Botting.IBotCommand, Grimoire]], mscorlib", "$values": [{"$type": "Grimoire.Botting.Commands.Map.CmdJ<PERSON>n, Grimoire", "Map": "legionarena-1", "Cell": "Enter", "Pad": "Spawn"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "ACCEPT"}, {"$type": "Grimoire.Botting.Commands.Quest.CmdAcceptQuest, Grimoire", "Quest": {"$type": "Grimoire.Game.Data.Quest, Grimoire", "QuestID": 6743, "IsInProgress": true}}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "ITEMS"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdNotInTemp, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is not in temp", "Value1": "<PERSON><PERSON><PERSON>' Brooch", "Value2": "1"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "1"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "TURN IN"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "1"}, {"$type": "Grimoire.Botting.Commands.Map.CmdMoveTo<PERSON>ell, Grimoire", "Cell": "Boss", "Pad": "Left"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "K1"}, {"$type": "Grimoire.Botting.Commands.Combat.CmdSkillSet, Grimoire", "Name": "SKILL SET NAME"}, {"$type": "Grimoire.Botting.Commands.Combat.Cmd<PERSON>ill, Grimoire", "Monster": "*", "KillPriority": ""}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdNotInTemp, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is not in temp", "Value1": "<PERSON><PERSON><PERSON>' Brooch", "Value2": "1"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "K1"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "ITEMS"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "TURN IN"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdDelay, Grimoire", "Delay": 100}, {"$type": "Grimoire.Botting.Commands.Quest.CmdCompleteQuest, Grimoire", "Quest": {"$type": "Grimoire.Game.Data.Quest, Grimoire", "QuestID": 6743, "IsInProgress": true}, "CompleteTry": 1, "Delay": 1000}, {"$type": "Grimoire.Botting.Commands.Misc.CmdDelay, Grimoire", "Delay": 100}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "ACCEPT"}]}, "Skills": {"$type": "System.Collections.Generic.List`1[[Grimoire.Game.Data.Skill, Grimoire]], mscorlib", "$values": [{"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "[SKILL SET NAME]", "Type": 2}, {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "Depraved Empowerment", "Index": "3"}, {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "Wicked Purgatory", "Index": "2"}, {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "Atramentous Shade", "Index": "1"}, {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "Anathema", "Index": "4"}]}, "Quests": {"$type": "System.Collections.Generic.List`1[[Grimoire.Game.Data.Quest, Grimoire]], mscorlib", "$values": []}, "Author": "", "Description": "{\\rtf1\\ansi\\ansicpg1252\\deff0\\deflang1033{\\fonttbl{\\f0\\fnil\\fcharset0 Microsoft Sans Serif;}}\r\n{\\colortbl ;\\red220\\green220\\blue220;}\r\n\\viewkind4\\uc1\\pard\\cf1\\b\\f0\\fs29\\par\r\n}\r\n", "Boosts": {"$type": "System.Collections.Generic.List`1[[Grimoire.Game.Data.InventoryItem, Grimoire]], mscorlib", "$values": []}, "Drops": {"$type": "System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib", "$values": ["Legion Token"]}, "Server": {"$type": "Grimoire.Game.Data.Server, Grimoire", "sName": "Twilly", "iPort": 5588, "sIP": "twilly.aqw.aq.com"}, "ExitCombatBeforeRest": true, "ExitCombatBeforeQuest": true, "EnablePickup": true, "EnableRejection": true, "RelogDelay": 5000, "BotDelay": 1000, "SkipDelayIndexIf": true, "WalkSpeed": 8, "NotifyUponDrop": {"$type": "System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib", "$values": []}, "RestMp": 60, "RestHp": 60, "RestartUponDeath": true, "Items": {"$type": "System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib", "$values": []}, "DropDelay": 500, "FollowName": "Player"}