{"$type": "Grimoire.Botting.Configuration, Grimoire", "Commands": {"$type": "System.Collections.Generic.List`1[[Grimoire.Botting.IBotCommand, Grimoire]], mscorlib", "$values": [{"$type": "Grimoire.Botting.Commands.Combat.CmdShortHunt, Grimoire", "Map": "battleunderc", "Cell": "r5", "Pad": "Left", "Monster": "*", "ItemName": "Cavern <PERSON>", "Quantity": "1600", "AfterKills": 1, "DelayAfterKill": 500, "BlankFirst": true}, {"$type": "Grimoire.Botting.Commands.Combat.CmdShortHunt, Grimoire", "Map": "<PERSON><PERSON>dred", "Cell": "Enter", "Pad": "Spawn", "Monster": "*", "ItemName": "(<PERSON><PERSON><PERSON>) Scroll of Dark Arts", "Quantity": "4", "AfterKills": 1, "DelayAfterKill": 500, "BlankFirst": true}, {"$type": "Grimoire.Botting.Commands.Combat.CmdShortHunt, Grimoire", "Map": "bosschallenge", "Cell": "r18", "Pad": "Left", "Monster": "*", "ItemName": "Prima<PERSON>'s <PERSON>lt", "Quantity": "3", "AfterKills": 1, "DelayAfterKill": 500, "BlankFirst": true}, {"$type": "Grimoire.Botting.Commands.Combat.CmdShortHunt, Grimoire", "Map": "chaoscrypt-a", "Cell": "Boss2", "Pad": "Left", "Monster": "*", "ItemName": "<PERSON>", "Quantity": "2", "AfterKills": 1, "DelayAfterKill": 500, "BlankFirst": true}, {"$type": "Grimoire.Botting.Commands.Combat.CmdShortHunt, Grimoire", "Map": "timespace", "Cell": "Frame2", "Pad": "Left", "Monster": "*", "ItemName": "Chaorrupted Hourglass", "Quantity": "31", "AfterKills": 1, "DelayAfterKill": 1500, "BlankFirst": true}, {"$type": "Grimoire.Botting.Commands.Combat.CmdShortHunt, Grimoire", "Map": "sepulchurebattle", "Cell": "Enter", "Pad": "Spawn", "Monster": "*", "ItemName": "Doom Heart", "Quantity": "1", "AfterKills": 1, "DelayAfterKill": 500, "BlankFirst": true}, {"$type": "Grimoire.Botting.Commands.Misc.CmdPing, Grimoire"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdStop, Grimoire"}]}, "Skills": {"$type": "System.Collections.Generic.List`1[[Grimoire.Game.Data.Skill, Grimoire]], mscorlib", "$values": [{"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "Armageddon", "Index": "4"}, {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "Shackle", "Index": "1", "Type": 1, "SType": 1, "SafeValue": 60}, {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "<PERSON>lord's Gaze", "Index": "2"}, {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "Unshackle", "Index": "3", "Type": 1, "SType": 1, "SafeValue": 60}]}, "Quests": {"$type": "System.Collections.Generic.List`1[[Grimoire.Game.Data.Quest, Grimoire]], mscorlib", "$values": []}, "Author": "Author", "Description": "Description", "Boosts": {"$type": "System.Collections.Generic.List`1[[Grimoire.Game.Data.InventoryItem, Grimoire]], mscorlib", "$values": []}, "Drops": {"$type": "System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib", "$values": ["(<PERSON><PERSON><PERSON>) Scroll of Dark Arts", "Prima<PERSON>'s <PERSON>lt", "<PERSON>", "Chaorrupted Hourglass", "Doom Heart", "Cavern <PERSON>"]}, "Server": {"$type": "Grimoire.Game.Data.Server, Grimoire", "sName": "Artix", "iPort": 4563}, "SkillDelay": 100, "ExitCombatBeforeRest": true, "ExitCombatBeforeQuest": true, "DropDelay": 1000, "EnablePickup": true, "AutoRelogin": true, "RelogDelay": 5000, "RelogRetryUponFailure": true, "BotDelay": 1000, "SkipDelayIndexIf": true, "InfiniteAttackRange": true, "DisableAnimations": true, "WalkSpeed": 8, "NotifyUponDrop": {"$type": "System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib", "$values": []}, "RestMp": 60, "RestHp": 60, "RestartUponDeath": true}