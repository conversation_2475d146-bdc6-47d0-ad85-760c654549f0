{"$type": "Grimoire.Botting.Configuration, Grimoire", "Commands": {"$type": "System.Collections.Generic.List`1[[Grimoire.Botting.IBotCommand, Grimoire]], mscorlib", "$values": [{"$type": "Grimoire.Botting.Commands.Map.CmdMoveTo<PERSON>ell, Grimoire", "Cell": "Blank", "Pad": "Spawn"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdDelay, Grimoire", "Delay": 2000}, {"$type": "Grimoire.Botting.Commands.Quest.CmdAcceptQuest, Grimoire", "Quest": {"$type": "Grimoire.Game.Data.Quest, Grimoire", "QuestID": 5660, "Text": ""}}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdQuestCompleted, Grimoire", "Tag": "Quest", "Text": "Quest can be turned in", "Value1": "5660", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "QUEST"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdNotInInventory, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is not in inventory", "Value1": "Hadean Onyx of Nulgath", "Value2": "1"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "HADEAN ONYX"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdNotInInventory, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is not in inventory", "Value1": "Elders' Blood", "Value2": "1"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "ELDERS BLOOD"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdNotInInventory, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is not in inventory", "Value1": "Unidentified 13", "Value2": "1"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "UNI 13"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdNotInInventory, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is not in inventory", "Value1": "Tainted Gem", "Value2": "100"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "TAINTED GEM"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdNotInInventory, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is not in inventory", "Value1": "Bone Dust", "Value2": "20"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "BONE DUST"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdNotInInventory, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is not in inventory", "Value1": "Emblem of Nulgath", "Value2": "20"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "EMBLEM OF NULGATH"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdNotInInventory, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is not in inventory", "Value1": "Gem of Nulgath", "Value2": "20"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "GEM OF NULGATH"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdNotInInventory, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is not in inventory", "Value1": "Essence of Nulgath", "Value2": "50"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "ESSENCE OF NULGATH"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdNotInInventory, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is not in inventory", "Value1": "Archfiend's Favor", "Value2": "300"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "AF & NA"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdNotInInventory, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is not in inventory", "Value1": "<PERSON><PERSON><PERSON><PERSON>'s <PERSON><PERSON><PERSON><PERSON>", "Value2": "300"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "AF & NA"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdNotInInventory, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is not in inventory", "Value1": "<PERSON><PERSON><PERSON><PERSON>", "Value2": "1"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "NULGATH CHOCO"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdNotInInventory, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is not in inventory", "Value1": "Elemental Ink", "Value2": "10"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "ELEMNTAL INK"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdNotInInventory, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is not in inventory", "Value1": "Black Knight Orb", "Value2": "1"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "BK ORB"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdNotInInventory, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is not in inventory", "Value1": "The Secret 1", "Value2": "1"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "THE SECRET 1"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdNotInInventory, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is not in inventory", "Value1": "<PERSON><PERSON><PERSON>", "Value2": "1"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "DWAKEL DECODER"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdNotInInventory, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is not in inventory", "Value1": "Aelita's Emerald", "Value2": "1"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "AELITA EMERALD"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdRestart, Grimoire"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "---"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "HADEAN ONYX"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdMapIsNot, Grimoire", "Tag": "Map", "Text": "Map is not", "Value1": "terc<PERSON><PERSON><PERSON><PERSON>", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Map.CmdJ<PERSON>n, Grimoire", "Map": "citadel", "Cell": "m22", "Pad": "Left"}, {"$type": "Grimoire.Botting.Commands.Map.CmdJ<PERSON>n, Grimoire", "Map": "terc<PERSON><PERSON><PERSON><PERSON>", "Cell": "m4", "Pad": "Left"}, {"$type": "Grimoire.Botting.Commands.Combat.CmdKill<PERSON>or, Grimoire", "Monster": "Shadow of Nulgath", "ItemName": "Hadean Onyx of Nulgath", "Quantity": "1", "AfterKills": 1, "DelayAfterKill": 500}, {"$type": "Grimoire.Botting.Commands.Misc.CmdRestart, Grimoire"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "UNI 13"}, {"$type": "Grimoire.Botting.Commands.Item.CmdBankTransfer, Grimoire", "TransferFromBank": true, "ItemName": "<PERSON><PERSON><PERSON><PERSON>'s Birthday Gift"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdInInventory, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is in inventory", "Value1": "<PERSON><PERSON><PERSON><PERSON>'s Birthday Gift", "Value2": "1"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "NULGATH BIRTHDAY"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "LARVAE"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdRestart, Grimoire"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "NULGATH BIRTHDAY"}, {"$type": "Grimoire.Botting.Commands.Map.CmdMoveTo<PERSON>ell, Grimoire", "Cell": "Blank", "Pad": "Spawn"}, {"$type": "Grimoire.Botting.Commands.Quest.CmdAcceptQuest, Grimoire", "Quest": {"$type": "Grimoire.Game.Data.Quest, Grimoire", "QuestID": 6697, "Text": ""}}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdQuestCompleted, Grimoire", "Tag": "Quest", "Text": "Quest can be turned in", "Value1": "6697", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "NULGATH BIRTHDAY QUEST"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdNotInTemp, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is not in temp", "Value1": "Slugfit Horn", "Value2": "5"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "SLUGFIT"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdNotInTemp, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is not in temp", "Value1": "Makai Fang", "Value2": "5"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "MAKAI"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdNotInTemp, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is not in temp", "Value1": "Imp Flame", "Value2": "3"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "IMP"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdNotInTemp, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is not in temp", "Value1": "<PERSON><PERSON><PERSON>", "Value2": "3"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "CYCLOP"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdNotInTemp, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is not in temp", "Value1": "Wereboar Tusk", "Value2": "2"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "WEREBOARD"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "SLUGFIT"}, {"$type": "Grimoire.Botting.Commands.Map.CmdJ<PERSON>n, Grimoire", "Map": "mobius-1234", "Cell": "Slugfit", "Pad": "Bottom"}, {"$type": "Grimoire.Botting.Commands.Map.CmdMoveTo<PERSON>ell, Grimoire", "Cell": "Slugfit", "Pad": "Bottom"}, {"$type": "Grimoire.Botting.Commands.Combat.CmdKill<PERSON>or, Grimoire", "Monster": "*", "ItemName": "Slugfit Horn", "ItemType": 1, "Quantity": "5", "AfterKills": 1, "DelayAfterKill": 500}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "NULGATH BIRTHDAY"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "MAKAI"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdMapIsNot, Grimoire", "Tag": "Map", "Text": "Map is not", "Value1": "terc<PERSON><PERSON><PERSON><PERSON>", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Map.CmdJ<PERSON>n, Grimoire", "Map": "citadel-1234", "Cell": "m22", "Pad": "Left"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdDelay, Grimoire", "Delay": 500}, {"$type": "Grimoire.Botting.Commands.Map.CmdJ<PERSON>n, Grimoire", "Map": "tercessuinotlim-1234", "Cell": "m2", "Pad": "Left"}, {"$type": "Grimoire.Botting.Commands.Combat.CmdKill<PERSON>or, Grimoire", "Monster": "*", "ItemName": "Makai Fang", "ItemType": 1, "Quantity": "5", "AfterKills": 1, "DelayAfterKill": 500}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "NULGATH BIRTHDAY"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "IMP"}, {"$type": "Grimoire.Botting.Commands.Map.CmdJ<PERSON>n, Grimoire", "Map": "hydra-1234", "Cell": "Rune2", "Pad": "Left"}, {"$type": "Grimoire.Botting.Commands.Combat.CmdKill<PERSON>or, Grimoire", "Monster": "*", "ItemName": "Imp Flame", "ItemType": 1, "Quantity": "3", "AfterKills": 1, "DelayAfterKill": 500}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "NULGATH BIRTHDAY"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "CYCLOP"}, {"$type": "Grimoire.Botting.Commands.Map.CmdJ<PERSON>n, Grimoire", "Map": "faerie-1234", "Cell": "End", "Pad": "Center"}, {"$type": "Grimoire.Botting.Commands.Combat.CmdKill<PERSON>or, Grimoire", "Monster": "Cyclops <PERSON>lord", "ItemName": "<PERSON><PERSON><PERSON>", "ItemType": 1, "Quantity": "3", "AfterKills": 1, "DelayAfterKill": 500}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "NULGATH BIRTHDAY"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "WEREBOARD"}, {"$type": "Grimoire.Botting.Commands.Map.CmdJ<PERSON>n, Grimoire", "Map": "greenguardwest-1234", "Cell": "West12", "Pad": "Up"}, {"$type": "Grimoire.Botting.Commands.Combat.CmdKill<PERSON>or, Grimoire", "Monster": "*", "ItemName": "Wereboar Tusk", "ItemType": 1, "Quantity": "2", "AfterKills": 1, "DelayAfterKill": 500}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "NULGATH BIRTHDAY"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdRestart, Grimoire"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "NULGATH BIRTHDAY QUEST"}, {"$type": "Grimoire.Botting.Commands.Quest.CmdCompleteQuest, Grimoire", "Quest": {"$type": "Grimoire.Game.Data.Quest, Grimoire", "QuestID": 6697, "Text": ""}, "CompleteTry": 1, "InBlank": true, "Delay": 1000}, {"$type": "Grimoire.Botting.Commands.Misc.CmdDelay, Grimoire", "Delay": 1000}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdInInventory, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is in inventory", "Value1": "Voucher of Nulgath", "Value2": "1"}, {"$type": "Grimoire.Botting.Commands.Item.Cmd<PERSON>ell, Grimoire", "ItemName": "Voucher of Nulgath"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdNotInInventory, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is not in inventory", "Value1": "Unidentified 13", "Value2": "1"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "NULGATH BIRTHDAY"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdRestart, Grimoire"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "LARVAE"}, {"$type": "Grimoire.Botting.Commands.Quest.CmdAcceptQuest, Grimoire", "Quest": {"$type": "Grimoire.Game.Data.Quest, Grimoire", "QuestID": 2566, "Text": ""}}, {"$type": "Grimoire.Botting.Commands.Map.CmdJ<PERSON>n, Grimoire", "Map": "elemental", "Cell": "r5", "Pad": "Left"}, {"$type": "Grimoire.Botting.Commands.Combat.CmdKill<PERSON>or, Grimoire", "Monster": "*", "ItemName": "Mana Energy for Nulgath", "Quantity": "1", "AfterKills": 1, "DelayAfterKill": 500}, {"$type": "Grimoire.Botting.Commands.Map.CmdMoveTo<PERSON>ell, Grimoire", "Cell": "r3", "Pad": "Left"}, {"$type": "Grimoire.Botting.Commands.Combat.CmdKill<PERSON>or, Grimoire", "Monster": "*", "ItemName": "Charged Mana Energy for Nulgath", "ItemType": 1, "Quantity": "5", "AfterKills": 1, "DelayAfterKill": 500}, {"$type": "Grimoire.Botting.Commands.Quest.CmdCompleteQuest, Grimoire", "Quest": {"$type": "Grimoire.Game.Data.Quest, Grimoire", "QuestID": 2566, "Text": ""}, "CompleteTry": 1, "InBlank": true, "Delay": 1000}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdInInventory, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is in inventory", "Value1": "Voucher of Nulgath", "Value2": "1"}, {"$type": "Grimoire.Botting.Commands.Item.Cmd<PERSON>ell, Grimoire", "ItemName": "Voucher of Nulgath"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdNotInInventory, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is not in inventory", "Value1": "Unidentified 13", "Value2": "1"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "LARVAE"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdRestart, Grimoire"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "ELDERS BLOOD"}, {"$type": "Grimoire.Botting.Commands.Quest.CmdAcceptQuest, Grimoire", "Quest": {"$type": "Grimoire.Game.Data.Quest, Grimoire", "QuestID": 802, "Text": ""}}, {"$type": "Grimoire.Botting.Commands.Misc.CmdProvoke, Grimoire", "Set": true}, {"$type": "Grimoire.Botting.Commands.Combat.CmdShortHunt, Grimoire", "Map": "arcangrove", "Cell": "Right", "Pad": "Right", "Monster": "*", "ItemName": "<PERSON><PERSON>", "ItemType": 1, "Quantity": "50", "AfterKills": 1, "DelayAfterKill": 500}, {"$type": "Grimoire.Botting.Commands.Misc.CmdProvoke, Grimoire"}, {"$type": "Grimoire.Botting.Commands.Quest.CmdCompleteQuest, Grimoire", "Quest": {"$type": "Grimoire.Game.Data.Quest, Grimoire", "QuestID": 802, "Text": ""}, "CompleteTry": 1, "InBlank": true, "Delay": 1000}, {"$type": "Grimoire.Botting.Commands.Misc.CmdRestart, Grimoire"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "TAINTED GEM"}, {"$type": "Grimoire.Botting.Commands.Quest.CmdAcceptQuest, Grimoire", "Quest": {"$type": "Grimoire.Game.Data.Quest, Grimoire", "QuestID": 568, "Text": ""}}, {"$type": "Grimoire.Botting.Commands.Misc.CmdProvoke, Grimoire", "Set": true}, {"$type": "Grimoire.Botting.Commands.Combat.CmdShortHunt, Grimoire", "Map": "battleunderb", "Cell": "Enter", "Pad": "Spawn", "Monster": "*", "ItemName": "Bone Dust", "Quantity": "2600", "IsGetDrops": true, "AfterKills": 5, "DelayAfterKill": 500}, {"$type": "Grimoire.Botting.Commands.Misc.CmdProvoke, Grimoire"}, {"$type": "Grimoire.Botting.Commands.Map.CmdMoveTo<PERSON>ell, Grimoire", "Cell": "Blank", "Pad": "Spawn"}, {"$type": "Grimoire.Botting.Commands.Quest.CmdCompleteQuest, Grimoire", "Quest": {"$type": "Grimoire.Game.Data.Quest, Grimoire", "QuestID": 568, "Text": ""}, "CompleteTry": 105, "InBlank": true, "Delay": 1000}, {"$type": "Grimoire.Botting.Commands.Misc.CmdRestart, Grimoire"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "BONE DUST"}, {"$type": "Grimoire.Botting.Commands.Combat.CmdShortHunt, Grimoire", "Map": "battleunderb", "Cell": "Enter", "Pad": "Spawn", "Monster": "*", "ItemName": "Bone Dust", "Quantity": "20", "IsGetDrops": true, "AfterKills": 5, "DelayAfterKill": 500}, {"$type": "Grimoire.Botting.Commands.Misc.CmdRestart, Grimoire"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "EMBLEM OF NULGATH"}, {"$type": "Grimoire.Botting.Commands.Item.CmdBankTransfer, Grimoire", "TransferFromBank": true, "ItemName": "Gem of Domination"}, {"$type": "Grimoire.Botting.Commands.Item.CmdBankTransfer, Grimoire", "TransferFromBank": true, "ItemName": "Fiend Seal"}, {"$type": "Grimoire.Botting.Commands.Quest.CmdAcceptQuest, Grimoire", "Quest": {"$type": "Grimoire.Game.Data.Quest, Grimoire", "QuestID": 4748, "Text": ""}}, {"$type": "Grimoire.Botting.Commands.Map.CmdJ<PERSON>n, Grimoire", "Map": "shadowblast", "Cell": "r13", "Pad": "Left"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdProvoke, Grimoire", "Set": true}, {"$type": "Grimoire.Botting.Commands.Combat.CmdKill<PERSON>or, Grimoire", "Monster": "Legion Fenrir", "ItemName": "Fiend Seal", "Quantity": "25", "AfterKills": 1, "DelayAfterKill": 500}, {"$type": "Grimoire.Botting.Commands.Combat.CmdKill<PERSON>or, Grimoire", "Monster": "Shadowrise Guard", "ItemName": "Gem of Domination", "Quantity": "1", "AfterKills": 1, "DelayAfterKill": 500}, {"$type": "Grimoire.Botting.Commands.Misc.CmdProvoke, Grimoire"}, {"$type": "Grimoire.Botting.Commands.Quest.CmdCompleteQuest, Grimoire", "Quest": {"$type": "Grimoire.Game.Data.Quest, Grimoire", "QuestID": 4748, "Text": ""}, "CompleteTry": 1, "InBlank": true, "Delay": 1000}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdNotInInventory, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is not in inventory", "Value1": "Emblem of Nulgath", "Value2": "20"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "EMBLEM OF NULGATH"}, {"$type": "Grimoire.Botting.Commands.Item.CmdBankTransfer, Grimoire", "ItemName": "Gem of Domination"}, {"$type": "Grimoire.Botting.Commands.Item.CmdBankTransfer, Grimoire", "ItemName": "Fiend Seal"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdRestart, Grimoire"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "GEM OF NULGATH"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdMapIsNot, Grimoire", "Tag": "Map", "Text": "Map is not", "Value1": "terc<PERSON><PERSON><PERSON><PERSON>", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Map.CmdJ<PERSON>n, Grimoire", "Map": "citadel", "Cell": "m22", "Pad": "Left"}, {"$type": "Grimoire.Botting.Commands.Quest.CmdAcceptQuest, Grimoire", "Quest": {"$type": "Grimoire.Game.Data.Quest, Grimoire", "QuestID": 4778, "Text": ""}}, {"$type": "Grimoire.Botting.Commands.Map.CmdJ<PERSON>n, Grimoire", "Map": "terc<PERSON><PERSON><PERSON><PERSON>", "Cell": "m2", "Pad": "Left"}, {"$type": "Grimoire.Botting.Commands.Combat.CmdKill<PERSON>or, Grimoire", "Monster": "*", "ItemName": "Essence of Nulgath", "Quantity": "60", "AfterKills": 3, "DelayAfterKill": 500}, {"$type": "Grimoire.Botting.Commands.Map.CmdMoveTo<PERSON>ell, Grimoire", "Cell": "Blank", "Pad": "Spawn"}, {"$type": "Grimoire.Botting.Commands.Quest.CmdCompleteQuest, Grimoire", "Quest": {"$type": "Grimoire.Game.Data.Quest, Grimoire", "QuestID": 4778, "ItemId": "6136", "Text": ""}, "CompleteTry": 1, "InBlank": true, "Delay": 1000}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdNotInInventory, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is not in inventory", "Value1": "Gem of Nulgath", "Value2": "20"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "GEM OF NULGATH"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdRestart, Grimoire"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "ESSENCE OF NULGATH"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdMapIsNot, Grimoire", "Tag": "Map", "Text": "Map is not", "Value1": "terc<PERSON><PERSON><PERSON><PERSON>", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Map.CmdJ<PERSON>n, Grimoire", "Map": "citadel", "Cell": "m22", "Pad": "Left"}, {"$type": "Grimoire.Botting.Commands.Map.CmdJ<PERSON>n, Grimoire", "Map": "terc<PERSON><PERSON><PERSON><PERSON>", "Cell": "m2", "Pad": "Left"}, {"$type": "Grimoire.Botting.Commands.Combat.CmdKill<PERSON>or, Grimoire", "Monster": "*", "ItemName": "Essence of Nulgath", "Quantity": "50", "AfterKills": 3, "DelayAfterKill": 500}, {"$type": "Grimoire.Botting.Commands.Misc.CmdRestart, Grimoire"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "AF & NA"}, {"$type": "Grimoire.Botting.Commands.Map.CmdJ<PERSON>n, Grimoire", "Map": "<PERSON><PERSON><PERSON><PERSON>", "Cell": "r2", "Pad": "Up"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdProvoke, Grimoire", "Set": true}, {"$type": "Grimoire.Botting.Commands.Combat.CmdKill<PERSON>or, Grimoire", "Monster": "*", "ItemName": "Archfiend's Favor", "Quantity": "300", "IsGetDrops": true, "AfterKills": 5, "DelayAfterKill": 500}, {"$type": "Grimoire.Botting.Commands.Combat.CmdKill<PERSON>or, Grimoire", "Monster": "*", "ItemName": "<PERSON><PERSON><PERSON><PERSON>'s <PERSON><PERSON><PERSON><PERSON>", "Quantity": "300", "IsGetDrops": true, "AfterKills": 5, "DelayAfterKill": 500}, {"$type": "Grimoire.Botting.Commands.Misc.CmdProvoke, Grimoire"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdRestart, Grimoire"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "NULGATH CHOCO"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdGoldLessThan, Grimoire", "Tag": "This player", "Text": "Gold is less than", "Value1": "2000000", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdIndex, Grimoire", "Type": 1, "Index": 4}, {"$type": "Grimoire.Botting.Commands.Map.CmdJ<PERSON>n, Grimoire", "Map": "citadel", "Cell": "Enter", "Pad": "Spawn"}, {"$type": "Grimoire.Botting.Commands.Item.CmdBuy, Grimoire", "ShopId": 44, "ItemName": "<PERSON><PERSON><PERSON><PERSON>"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdRestart, Grimoire"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdMapIsNot, Grimoire", "Tag": "Map", "Text": "Map is not", "Value1": "greenguardwest", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Map.CmdJ<PERSON>n, Grimoire", "Map": "greenguardwest-1234", "Cell": "West12", "Pad": "Up"}, {"$type": "Grimoire.Botting.Commands.Quest.CmdAcceptQuest, Grimoire", "Quest": {"$type": "Grimoire.Game.Data.Quest, Grimoire", "QuestID": 236, "Text": ""}}, {"$type": "Grimoire.Botting.Commands.Combat.CmdKill<PERSON>or, Grimoire", "Monster": "*", "ItemName": "Were Egg", "ItemType": 1, "Quantity": "1", "AfterKills": 5, "DelayAfterKill": 500}, {"$type": "Grimoire.Botting.Commands.Quest.CmdCompleteQuest, Grimoire", "Quest": {"$type": "Grimoire.Game.Data.Quest, Grimoire", "QuestID": 236, "Text": ""}, "CompleteTry": 1, "Delay": 1000}, {"$type": "Grimoire.Botting.Commands.Misc.CmdDelay, Grimoire", "Delay": 100}, {"$type": "Grimoire.Botting.Commands.Item.Cmd<PERSON>ell, Grimoire", "ItemName": "<PERSON><PERSON><PERSON><PERSON>"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "NULGATH CHOCO"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "ELEMNTAL INK"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdInInventory, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is in inventory", "Value1": "Mystic Quills", "Value2": "8"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdIndex, Grimoire", "Type": 1, "Index": 4}, {"$type": "Grimoire.Botting.Commands.Map.CmdJ<PERSON>n, Grimoire", "Map": "faerie-1234", "Cell": "End", "Pad": "Center"}, {"$type": "Grimoire.Botting.Commands.Combat.CmdKill<PERSON>or, Grimoire", "Monster": "Cyclops <PERSON>lord", "ItemName": "Mystic Quills", "Quantity": "8", "AfterKills": 5, "DelayAfterKill": 500}, {"$type": "Grimoire.Botting.Commands.Misc.CmdRestart, Grimoire"}, {"$type": "Grimoire.Botting.Commands.Map.CmdJ<PERSON>n, Grimoire", "Map": "spellcraft", "Cell": "Enter", "Pad": "Spawn"}, {"$type": "Grimoire.Botting.Commands.Item.CmdBuy, Grimoire", "ShopId": 549, "ItemName": "Elemental Ink"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdNotInInventory, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is not in inventory", "Value1": "Elemental Ink", "Value2": "20"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdIndex, Grimoire", "Index": 2}, {"$type": "Grimoire.Botting.Commands.Misc.CmdRestart, Grimoire"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "THE SECRET 1"}, {"$type": "Grimoire.Botting.Commands.Quest.CmdAcceptQuest, Grimoire", "Quest": {"$type": "Grimoire.Game.Data.Quest, Grimoire", "QuestID": 623, "Text": ""}}, {"$type": "Grimoire.Botting.Commands.Combat.CmdShortHunt, Grimoire", "Map": "willowcreek-1234", "Cell": "Yard2", "Pad": "Right", "Monster": "Hidden Spy", "ItemName": "The Secret 1", "Quantity": "1", "AfterKills": 5, "DelayAfterKill": 500}, {"$type": "Grimoire.Botting.Commands.Misc.CmdRestart, Grimoire"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "BK ORB"}, {"$type": "Grimoire.Botting.Commands.Quest.CmdAcceptQuest, Grimoire", "Quest": {"$type": "Grimoire.Game.Data.Quest, Grimoire", "QuestID": 318, "Text": ""}}, {"$type": "Grimoire.Botting.Commands.Combat.CmdShortHunt, Grimoire", "Map": "well", "Cell": "Boss", "Pad": "Center", "Monster": "*", "ItemName": "Black Knight Leg Piece", "ItemType": 1, "Quantity": "1", "AfterKills": 5, "DelayAfterKill": 500}, {"$type": "Grimoire.Botting.Commands.Combat.CmdShortHunt, Grimoire", "Map": "deathgazer", "Cell": "Enter", "Pad": "Spawn", "Monster": "*", "ItemName": "Black Knight Shoulder Piece", "ItemType": 1, "Quantity": "1", "AfterKills": 5, "DelayAfterKill": 500}, {"$type": "Grimoire.Botting.Commands.Combat.CmdShortHunt, Grimoire", "Map": "trunk", "Cell": "Enter", "Pad": "Spawn", "Monster": "*", "ItemName": "Black Knight Arm Piece", "ItemType": 1, "Quantity": "1", "AfterKills": 5, "DelayAfterKill": 500}, {"$type": "Grimoire.Botting.Commands.Combat.CmdShortHunt, Grimoire", "Map": "greendragon", "Cell": "Boss", "Pad": "Left", "Monster": "*", "ItemName": "Black Knight Chest Piece", "ItemType": 1, "Quantity": "1", "AfterKills": 5, "DelayAfterKill": 500}, {"$type": "Grimoire.Botting.Commands.Quest.CmdCompleteQuest, Grimoire", "Quest": {"$type": "Grimoire.Game.Data.Quest, Grimoire", "QuestID": 318, "Text": ""}, "CompleteTry": 1, "Delay": 1000}, {"$type": "Grimoire.Botting.Commands.Misc.CmdRestart, Grimoire"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "DWAKEL DECODER"}, {"$type": "Grimoire.Botting.Commands.Map.CmdJ<PERSON>n, Grimoire", "Map": "crashsite", "Cell": "Farm2", "Pad": "Right"}, {"$type": "Grimoire.Botting.Commands.Item.CmdMapItem, Grimoire", "ItemId": 106}, {"$type": "Grimoire.Botting.Commands.Misc.CmdRestart, Grimoire"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "AELITA EMERALD"}, {"$type": "Grimoire.Botting.Commands.Map.CmdJ<PERSON>n, Grimoire", "Map": "yulgar", "Cell": "Enter", "Pad": "Spawn"}, {"$type": "Grimoire.Botting.Commands.Item.CmdBuy, Grimoire", "ShopId": 16, "ItemName": "Aelita's Emerald"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdRestart, Grimoire"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "QUEST"}, {"$type": "Grimoire.Botting.Commands.Map.CmdJ<PERSON>n, Grimoire", "Map": "battleon", "Cell": "Enter", "Pad": "Spawn"}, {"$type": "Grimoire.Botting.Commands.Item.CmdBankTransfer, Grimoire", "TransferFromBank": true, "ItemName": "Roentgenium of Nulgath"}, {"$type": "Grimoire.Botting.Commands.Quest.CmdCompleteQuest, Grimoire", "Quest": {"$type": "Grimoire.Game.Data.Quest, Grimoire", "QuestID": 5660, "Text": ""}, "CompleteTry": 1, "InBlank": true, "Delay": 1000}, {"$type": "Grimoire.Botting.Commands.Misc.CmdDelay, Grimoire", "Delay": 500}, {"$type": "Grimoire.Botting.Commands.Misc.CmdPing, Grimoire", "Count": 10}, {"$type": "Grimoire.Botting.Commands.Misc.CmdStop, Grimoire"}]}, "Skills": {"$type": "System.Collections.Generic.List`1[[Grimoire.Game.Data.Skill, Grimoire]], mscorlib", "$values": [{"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "<PERSON>uine P<PERSON>", "Index": "1", "Type": 1, "SType": 1, "SafeValue": 50}, {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "Mirroring Arcane", "Index": "2"}, {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "Malevolence", "Index": "3"}, {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "Crimson Ritual", "Index": "4", "Type": 1, "SType": 1, "SafeValue": 50}]}, "Quests": {"$type": "System.Collections.Generic.List`1[[Grimoire.Game.Data.Quest, Grimoire]], mscorlib", "$values": []}, "Author": "Froztt13", "Description": "Requirment : \r\n-Level 50\r\n-Rank 4 Spellcrafting\r\n\r\n*if u hv no Nulgath Birthday Gift pet, bot will automatically using Nulgath Larvae quest instead", "Boosts": {"$type": "System.Collections.Generic.List`1[[Grimoire.Game.Data.InventoryItem, Grimoire]], mscorlib", "$values": []}, "Drops": {"$type": "System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib", "$values": ["Gem of Nulgath", "Diamond of Nulgath", "Voucher of Nulgath", "Voucher of Nulgath (non-mem)", "<PERSON><PERSON>", "Dark Crystal Shard", "Totem of Nulgath", "Blood Gem of the Archfiend", "Unidentified 13", "Unidentified 10", "Tainted Gem", "Essence of Nulgath", "Slugfit Horn", "Mystic Quills", "Mana Energy for Nulgath", "Elders' Blood", "Bone Dust", "Fiend Seal", "Gem of Domination", "Archfiend's Favor", "<PERSON><PERSON><PERSON><PERSON>'s <PERSON><PERSON><PERSON><PERSON>", "Essence of Nulgath", "<PERSON><PERSON><PERSON><PERSON>", "The Secret 1", "Black Knight Orb", "Unidentified 10", "Roentgenium of Nulgath", "Void Highlord Armor", "<PERSON><PERSON> of the Highlord", "Highlord's Void Wrap", "<PERSON><PERSON><PERSON>", "Hadean Onyx of Nulgath", "Emblem of Nulgath"]}, "ExitCombatBeforeRest": true, "ExitCombatBeforeQuest": true, "EnablePickup": true, "EnableRejection": true, "AutoRelogin": true, "RelogDelay": 5000, "RelogRetryUponFailure": true, "BotDelay": 1000, "SkipDelayIndexIf": true, "InfiniteAttackRange": true, "WalkSpeed": 16, "NotifyUponDrop": {"$type": "System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib", "$values": ["Roentgenium of Nulgath"]}, "RestMp": 60, "RestHp": 60, "RestartUponDeath": true, "Items": {"$type": "System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib", "$values": []}, "DropDelay": 1000}