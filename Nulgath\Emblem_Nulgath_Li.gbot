{"$type": "Grimoire.Botting.Configuration, Grimoire", "Commands": {"$type": "System.Collections.Generic.List`1[[Grimoire.Botting.IBotCommand, Grimoire]], mscorlib", "$values": [{"$type": "Grimoire.Botting.Commands.Item.CmdBankTransfer, Grimoire", "TransferFromBank": true, "ItemName": "Fiend Seal"}, {"$type": "Grimoire.Botting.Commands.Item.CmdBankTransfer, Grimoire", "TransferFromBank": true, "ItemName": "Gem of Domination"}, {"$type": "Grimoire.Botting.Commands.Map.CmdJ<PERSON>n, Grimoire", "Map": "shadowblast", "Cell": "r13", "Pad": "Left"}, {"$type": "Grimoire.Botting.Commands.Combat.CmdKill<PERSON>or, Grimoire", "Monster": "Legion Fenrir", "ItemName": "Fiend Seal", "Quantity": "25", "AfterKills": 1, "DelayAfterKill": 500}, {"$type": "Grimoire.Botting.Commands.Combat.CmdKill<PERSON>or, Grimoire", "Monster": "Shadowrise Guard", "ItemName": "Gem of Domination", "Quantity": "1", "AfterKills": 1, "DelayAfterKill": 500}, {"$type": "Grimoire.Botting.Commands.Misc.CmdDelay, Grimoire", "Delay": 1000}, {"$type": "Grimoire.Botting.Commands.Misc.CmdIndex, Grimoire", "Index": 4}]}, "Skills": {"$type": "System.Collections.Generic.List`1[[Grimoire.Game.Data.Skill, Grimoire]], mscorlib", "$values": [{"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "Crimson Ritual", "Index": "4", "Type": 1, "SType": 1, "SafeValue": 50}, {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "Malevolence", "Index": "3"}, {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "Mirroring Arcane", "Index": "2"}, {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "<PERSON>uine P<PERSON>", "Index": "1", "Type": 1, "SType": 1, "SafeValue": 50}]}, "Quests": {"$type": "System.Collections.Generic.List`1[[Grimoire.Game.Data.Quest, Grimoire]], mscorlib", "$values": [{"$type": "Grimoire.Game.Data.Quest, Grimoire", "QuestID": 4748, "Text": "4748 [InBlank] [InBlank]", "CompleteInBlank": true}]}, "Author": "Author", "Description": "Description", "Boosts": {"$type": "System.Collections.Generic.List`1[[Grimoire.Game.Data.InventoryItem, Grimoire]], mscorlib", "$values": []}, "Drops": {"$type": "System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib", "$values": ["Fiend Seal", "Gem of Domination", "Emblem of Nulgath"]}, "Server": {"$type": "Grimoire.Game.Data.Server, Grimoire", "sName": "Galanoth", "iPort": 12915}, "SkillDelay": 100, "ExitCombatBeforeRest": true, "ExitCombatBeforeQuest": true, "DropDelay": 1000, "EnablePickup": true, "EnableRejection": true, "AutoRelogin": true, "RelogDelay": 5000, "BotDelay": 1000, "SkipDelayIndexIf": true, "InfiniteAttackRange": true, "ProvokeMonsters": true, "DisableAnimations": true, "WalkSpeed": 8, "NotifyUponDrop": {"$type": "System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib", "$values": []}, "RestMp": 60, "RestHp": 60, "RestartUponDeath": true}