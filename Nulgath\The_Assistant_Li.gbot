{"$type": "Grimoire.Botting.Configuration, Grimoire", "Commands": {"$type": "System.Collections.Generic.List`1[[Grimoire.Botting.IBotCommand, Grimoire]], mscorlib", "$values": [{"$type": "Grimoire.Botting.Commands.Quest.CmdAcceptQuest, Grimoire", "Quest": {"$type": "Grimoire.Game.Data.Quest, Grimoire", "QuestID": 2859, "Text": ""}}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdMapIsNot, Grimoire", "Tag": "Map", "Text": "Map is not", "Value1": "yulgar", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Map.CmdJ<PERSON>n, Grimoire", "Map": "yulgar-a", "Cell": "Enter", "Pad": "Spawn"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdNotInInventory, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is not in inventory", "Value1": "War-Torn Memorabilia", "Value2": "*"}, {"$type": "Grimoire.Botting.Commands.Item.CmdBuy, Grimoire", "ShopId": 41, "ItemName": "War-Torn Memorabilia"}, {"$type": "Grimoire.Botting.Commands.Quest.CmdCompleteQuest, Grimoire", "Quest": {"$type": "Grimoire.Game.Data.Quest, Grimoire", "QuestID": 2859, "Text": ""}, "CompleteTry": 1, "Delay": 1000}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdGoldGreaterThan, Grimoire", "Tag": "This player", "Text": "Gold is greater than", "Value1": "100000", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdRestart, Grimoire"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "GOLD"}, {"$type": "Grimoire.Botting.Commands.Map.CmdJ<PERSON>n, Grimoire", "Map": "greenguardwest-a", "Cell": "West12", "Pad": "Up"}, {"$type": "Grimoire.Botting.Commands.Combat.CmdKill<PERSON>or, Grimoire", "Monster": "*", "ItemName": "Were Egg", "ItemType": 1, "Quantity": "1", "AfterKills": 1, "DelayAfterKill": 500}, {"$type": "Grimoire.Botting.Commands.Misc.CmdDelay, Grimoire", "Delay": 500}, {"$type": "Grimoire.Botting.Commands.Item.Cmd<PERSON>ell, Grimoire", "ItemName": "<PERSON><PERSON><PERSON><PERSON>"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdGoldLessThan, Grimoire", "Tag": "This player", "Text": "Gold is less than", "Value1": "2000000", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "GOLD"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdRestart, Grimoire"}]}, "Skills": {"$type": "System.Collections.Generic.List`1[[Grimoire.Game.Data.Skill, Grimoire]], mscorlib", "$values": [{"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "Armageddon", "Index": "4"}, {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "Unshackle", "Index": "3", "Type": 1, "SType": 1, "SafeValue": 60}, {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "<PERSON>lord's Gaze", "Index": "2"}, {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "Shackle", "Index": "1", "Type": 1, "SType": 1, "SafeValue": 60}]}, "Quests": {"$type": "System.Collections.Generic.List`1[[Grimoire.Game.Data.Quest, Grimoire]], mscorlib", "$values": [{"$type": "Grimoire.Game.Data.Quest, Grimoire", "QuestID": 236, "Text": "236 [In<PERSON>lank]", "CompleteInBlank": true}]}, "Author": "Author", "Description": "Description", "Boosts": {"$type": "System.Collections.Generic.List`1[[Grimoire.Game.Data.InventoryItem, Grimoire]], mscorlib", "$values": []}, "Drops": {"$type": "System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib", "$values": ["Gem of Nulgath", "Diamond of Nulgath", "Voucher of Nulgath", "Voucher of Nulgath (non-mem)", "<PERSON><PERSON>", "Dark Crystal Shard", "Totem of Nulgath", "Blood Gem of the Archfiend", "Tainted Gem", "Unidentified 10", "Unidentified 13", "<PERSON><PERSON><PERSON><PERSON>"]}, "Server": {"$type": "Grimoire.Game.Data.Server, Grimoire", "sName": "Twilly", "iPort": 46030}, "SkillDelay": 100, "ExitCombatBeforeRest": true, "DropDelay": 1000, "EnablePickup": true, "RelogDelay": 5000, "BotDelay": 1000, "SkipDelayIndexIf": true, "InfiniteAttackRange": true, "DisableAnimations": true, "WalkSpeed": 8, "NotifyUponDrop": {"$type": "System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib", "$values": ["Unidentified 29"]}, "RestMp": 60, "RestHp": 60, "RestartUponDeath": true}