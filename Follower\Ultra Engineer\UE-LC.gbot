{"$type": "Grimoire.Botting.Configuration, Grimoire", "Commands": {"$type": "System.Collections.Generic.List`1[[Grimoire.Botting.IBotCommand, Grimoire]], mscorlib", "$values": [{"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdSetVar, Grimoire", "Tag": "Misc", "Text": "Set Temporary Variable", "Value1": "PLAYER", "Value2": "Remake"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdBlank3, G<PERSON>oire", "Text": "...", "Alpha": 1, "R": 220, "G": 220, "B": 220}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "GO"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdMonsterInRoom, Grimoire", "Tag": "Monster", "Text": "Is in room", "Value1": "Defense Drone", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "A-DEF"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdMonsterInRoom, Grimoire", "Tag": "Monster", "Text": "Is in room", "Value1": "Attack Drone", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "A-ATCK"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdMonsterInRoom, Grimoire", "Tag": "Monster", "Text": "Is in room", "Value1": "Ultra Engineer", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "A-ENGINEER"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdBlank3, G<PERSON>oire", "Text": "...", "Alpha": 1, "R": 220, "G": 220, "B": 220}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "S"}, {"$type": "Grimoire.Botting.Commands.Combat.CmdUseSkill, Grimoire", "Skill": {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "Lingering Light", "Index": "1"}, "Targeted": true}, {"$type": "Grimoire.Botting.Commands.Combat.CmdUseSkill, Grimoire", "Skill": {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "Light Blast", "Index": "2"}, "Targeted": true}, {"$type": "Grimoire.Botting.Commands.Combat.CmdUseSkill, Grimoire", "Skill": {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "Light Blast", "Index": "3"}, "Force": true, "Targeted": true}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdHealthLessThan, Grimoire", "Tag": "This player", "Text": "Health is less than", "Value1": "2700", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Combat.CmdUseSkill, Grimoire", "Skill": {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "Illuminate", "Index": "3"}, "Force": true}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdPlayersHPLessThan, Grimoire", "Tag": "Player", "Text": "Health is less than", "Value1": "[PLAYER]", "Value2": "70"}, {"$type": "Grimoire.Botting.Commands.Combat.CmdUseSkill, Grimoire", "Skill": {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "Illuminate", "Index": "3"}, "Force": true}, {"$type": "Grimoire.Botting.Commands.Combat.CmdUseSkill, Grimoire", "Skill": {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "Burned by Light", "Index": "4"}, "Targeted": true}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "GO"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdBlank3, G<PERSON>oire", "Text": "...", "Alpha": 1, "R": 220, "G": 220, "B": 220}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "A-DEF"}, {"$type": "Grimoire.Botting.Commands.Combat.CmdAttack, Grimoire", "Monster": "Defense Drone", "UseSkill": true}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "S"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdBlank3, G<PERSON>oire", "Text": "...", "Alpha": 1, "R": 220, "G": 220, "B": 220}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "A-ATCK"}, {"$type": "Grimoire.Botting.Commands.Combat.CmdAttack, Grimoire", "Monster": "Attack Drone", "UseSkill": true}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "S"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdBlank3, G<PERSON>oire", "Text": "...", "Alpha": 1, "R": 220, "G": 220, "B": 220}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "A-ENGINEER"}, {"$type": "Grimoire.Botting.Commands.Combat.CmdAttack, Grimoire", "Monster": "Ultra Engineer", "UseSkill": true}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "S"}]}, "Skills": {"$type": "System.Collections.Generic.List`1[[Grimoire.Game.Data.Skill, Grimoire]], mscorlib", "$values": []}, "Quests": {"$type": "System.Collections.Generic.List`1[[Grimoire.Game.Data.Quest, Grimoire]], mscorlib", "$values": []}, "Author": "", "Description": "{\\rtf1\\ansi\\ansicpg1252\\deff0\\deflang2057{\\fonttbl{\\f0\\fnil\\fcharset0 Microsoft Sans Serif;}}\r\n{\\colortbl ;\\red220\\green220\\blue220;}\r\n\\viewkind4\\uc1\\pard\\cf1\\b\\f0\\fs29 This is where information about a bot will be shown in RichTextFormat\\par\r\n}\r\n", "Boosts": {"$type": "System.Collections.Generic.List`1[[Grimoire.Game.Data.InventoryItem, Grimoire]], mscorlib", "$values": []}, "Drops": {"$type": "System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib", "$values": []}, "Server": {"$type": "Grimoire.Game.Data.Server, Grimoire", "sName": "Twilly", "iPort": 5588}, "ExitCombatBeforeRest": true, "ExitCombatBeforeQuest": true, "EnablePickup": true, "RelogDelay": 5000, "SkipDelayIndexIf": true, "InfiniteAttackRange": true, "WalkSpeed": 8, "NotifyUponDrop": {"$type": "System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib", "$values": []}, "RestMp": 60, "RestHp": 60, "RestartUponDeath": true, "Items": {"$type": "System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib", "$values": []}, "DropDelay": 500, "AntiCounter": true, "FollowCheck": true, "FollowName": "remake"}