{"$type": "Grimoire.Botting.Configuration, Grimoire", "Commands": {"$type": "System.Collections.Generic.List`1[[Grimoire.Botting.IBotCommand, Grimoire]], mscorlib", "$values": [{"$type": "Grimoire.Botting.Commands.Map.CmdMoveTo<PERSON>ell, Grimoire", "Cell": "Blank", "Pad": "Spawn"}, {"$type": "Grimoire.Botting.Commands.Quest.CmdAcceptQuest, Grimoire", "Quest": {"$type": "Grimoire.Game.Data.Quest, Grimoire", "QuestID": 6897, "Text": "", "IsInProgress": true}}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdNotInInventory, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is not in inventory", "Value1": "Tethered Soul", "Value2": "300"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "TETHERED SOUL"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdNotInInventory, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is not in inventory", "Value1": "<PERSON><PERSON>cus Empowered", "Value2": "50"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "AEACUS"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdProvoke, Grimoire", "Set": true}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdNotInInventory, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is not in inventory", "Value1": "Darkened Essence", "Value2": "500"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "DARKENED"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdNotInInventory, Grimoire", "Tag": "<PERSON><PERSON>", "Text": "Is not in inventory", "Value1": "<PERSON><PERSON><PERSON>tract", "Value2": "1000"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "DRACOLICH"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdProvoke, Grimoire"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "QUEST"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "AEACUS"}, {"$type": "Grimoire.Botting.Commands.Map.CmdJ<PERSON>n, Grimoire", "Map": "judgement", "Cell": "r10a", "Pad": "Left"}, {"$type": "Grimoire.Botting.Commands.Combat.CmdKill<PERSON>or, Grimoire", "Monster": "*", "ItemName": "<PERSON><PERSON>cus Empowered", "Quantity": "50", "AfterKills": 1, "DelayAfterKill": 500}, {"$type": "Grimoire.Botting.Commands.Map.CmdMoveTo<PERSON>ell, Grimoire", "Cell": "Blank", "Pad": "Spawn"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdRestart, Grimoire"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "TETHERED SOUL"}, {"$type": "Grimoire.Botting.Commands.Map.CmdJ<PERSON>n, Grimoire", "Map": "revenant", "Cell": "r2", "Pad": "Left"}, {"$type": "Grimoire.Botting.Commands.Combat.CmdKill<PERSON>or, Grimoire", "Monster": "*", "ItemName": "Tethered Soul", "Quantity": "300", "AfterKills": 1, "DelayAfterKill": 500}, {"$type": "Grimoire.Botting.Commands.Map.CmdMoveTo<PERSON>ell, Grimoire", "Cell": "Blank", "Pad": "Spawn"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdRestart, Grimoire"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "DARKENED"}, {"$type": "Grimoire.Botting.Commands.Map.CmdJ<PERSON>n, Grimoire", "Map": "shadowrealmpast", "Cell": "Enter", "Pad": "Spawn"}, {"$type": "Grimoire.Botting.Commands.Combat.CmdKill<PERSON>or, Grimoire", "Monster": "*", "ItemName": "Darkened Essence", "Quantity": "500", "IsGetDrops": true, "AfterKills": 3, "DelayAfterKill": 500}, {"$type": "Grimoire.Botting.Commands.Map.CmdMoveTo<PERSON>ell, Grimoire", "Cell": "Blank", "Pad": "Spawn"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdRestart, Grimoire"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "DRACOLICH"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdPacket, Grimoire", "Packet": "{\"t\":\"xt\",\"b\":{\"r\":-1,\"o\":{\"cmd\":\"updateQuest\",\"iValue\":30,\"iIndex\":77}}}", "SpamTimes": 1, "Delay": 1000, "ForClient": true}, {"$type": "Grimoire.Botting.Commands.Map.CmdJ<PERSON>n, Grimoire", "Map": "necrodungeon", "Cell": "r22", "Pad": "Down"}, {"$type": "Grimoire.Botting.Commands.Combat.CmdKill<PERSON>or, Grimoire", "Monster": "*", "ItemName": "<PERSON><PERSON><PERSON>tract", "Quantity": "1000", "IsGetDrops": true, "AfterKills": 3, "DelayAfterKill": 500}, {"$type": "Grimoire.Botting.Commands.Map.CmdMoveTo<PERSON>ell, Grimoire", "Cell": "Blank", "Pad": "Spawn"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdRestart, Grimoire"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "QUEST"}, {"$type": "Grimoire.Botting.Commands.Quest.CmdCompleteQuest, Grimoire", "Quest": {"$type": "Grimoire.Game.Data.Quest, Grimoire", "QuestID": 6897, "Text": "", "IsInProgress": true}, "CompleteTry": 2, "InBlank": true, "Delay": 1000}, {"$type": "Grimoire.Botting.Commands.Misc.CmdDelay, Grimoire", "Delay": 2000}, {"$type": "Grimoire.Botting.Commands.Misc.CmdRestart, Grimoire"}]}, "Skills": {"$type": "System.Collections.Generic.List`1[[Grimoire.Game.Data.Skill, Grimoire]], mscorlib", "$values": [{"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "Shackle", "Index": "1", "Type": 1, "SType": 1, "SafeValue": 60}, {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "<PERSON>lord's Gaze", "Index": "2"}, {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "Unshackle", "Index": "3", "Type": 1, "SType": 1, "SafeValue": 60}, {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "Armageddon", "Index": "4"}]}, "Quests": {"$type": "System.Collections.Generic.List`1[[Grimoire.Game.Data.Quest, Grimoire]], mscorlib", "$values": []}, "Author": "Author", "Description": "{\\rtf1\\ansi\\deff0{\\fonttbl{\\f0\\fnil\\fcharset0 Microsoft Sans Serif;}}\r\n{\\colortbl ;\\red220\\green220\\blue220;}\r\n\\viewkind4\\uc1\\pard\\cf1\\lang2057\\b\\f0\\fs29 Description\\par\r\n}\r\n", "Boosts": {"$type": "System.Collections.Generic.List`1[[Grimoire.Game.Data.InventoryItem, Grimoire]], mscorlib", "$values": []}, "Drops": {"$type": "System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib", "$values": ["<PERSON><PERSON>cus Empowered", "Tethered Soul", "Darkened Essence", "<PERSON><PERSON><PERSON>tract", "Revenant's Spellscroll"]}, "Server": {"$type": "Grimoire.Game.Data.Server, Grimoire", "sName": "Artix", "iPort": 31892}, "ExitCombatBeforeRest": true, "ExitCombatBeforeQuest": true, "EnablePickup": true, "EnableRejection": true, "AutoRelogin": true, "RelogDelay": 5000, "RelogRetryUponFailure": true, "BotDelay": 1000, "SkipDelayIndexIf": true, "InfiniteAttackRange": true, "LagKiller": true, "DisableAnimations": true, "WalkSpeed": 8, "NotifyUponDrop": {"$type": "System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib", "$values": []}, "RestMp": 60, "RestHp": 60, "RestartUponDeath": true, "Items": {"$type": "System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib", "$values": []}, "AFK": true, "DropDelay": 1000}