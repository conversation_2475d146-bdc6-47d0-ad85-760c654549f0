{"$type": "Grimoire.Botting.Configuration, Grimoire", "Commands": {"$type": "System.Collections.Generic.List`1[[Grimoire.Botting.IBotCommand, Grimoire]], mscorlib", "$values": [{"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdMapIsNot, Grimoire", "Tag": "Map", "Text": "Map is not", "Value1": "<PERSON><PERSON><PERSON>", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Map.CmdJ<PERSON>n, Grimoire", "Map": "<PERSON><PERSON><PERSON>", "Cell": "Enter", "Pad": "Spawn"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdBotDelay, Grimoire", "Delay": 500}, {"$type": "Grimoire.Botting.Commands.Misc.CmdPacket, Grimoire", "Packet": "%xt%zm%acceptQuest%163831%5233%", "SpamTimes": 1, "Delay": 500}, {"$type": "Grimoire.Botting.Commands.Misc.CmdPacket, Grimoire", "Packet": "%xt%zm%getMapItem%163831%4586%", "SpamTimes": 1, "Delay": 500}, {"$type": "Grimoire.Botting.Commands.Misc.CmdPacket, Grimoire", "Packet": "%xt%zm%getMapItem%163831%4595%", "SpamTimes": 1, "Delay": 500}, {"$type": "Grimoire.Botting.Commands.Misc.CmdPacket, Grimoire", "Packet": "%xt%zm%tryQuestComplete%163831%5233%-1%false%wvz%", "SpamTimes": 1, "Delay": 1000}, {"$type": "Grimoire.Botting.Commands.Misc.CmdIndex, Grimoire", "Index": 4}]}, "Skills": {"$type": "System.Collections.Generic.List`1[[Grimoire.Game.Data.Skill, Grimoire]], mscorlib", "$values": [{"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "1: Heartbeat", "Index": "1"}, {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "2: <PERSON><PERSON> Word", "Index": "2"}, {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "3: Energy Flow", "Index": "3"}, {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "4: Clear Mind", "Index": "4"}]}, "Quests": {"$type": "System.Collections.Generic.List`1[[Grimoire.Game.Data.Quest, Grimoire]], mscorlib", "$values": []}, "Author": "Author", "Description": "Description", "Boosts": {"$type": "System.Collections.Generic.List`1[[Grimoire.Game.Data.InventoryItem, Grimoire]], mscorlib", "$values": []}, "Drops": {"$type": "System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib", "$values": []}, "Server": {"$type": "Grimoire.Game.Data.Server, Grimoire", "sName": "Twilly", "iPort": 24844}, "ExitCombatBeforeRest": true, "EnablePickup": true, "RelogDelay": 5000, "BotDelay": 5000, "SkipDelayIndexIf": true, "InfiniteAttackRange": true, "WalkSpeed": 16, "NotifyUponDrop": {"$type": "System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib", "$values": []}, "RestMp": 60, "RestHp": 60, "RestartUponDeath": true}