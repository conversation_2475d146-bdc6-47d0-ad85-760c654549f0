{"$type": "Grimoire.Botting.Configuration, Grimoire", "Commands": {"$type": "System.Collections.Generic.List`1[[Grimoire.Botting.IBotCommand, Grimoire]], mscorlib", "$values": [{"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdSetVar, Grimoire", "Tag": "Misc", "Text": "Set Temporary Variable", "Value1": "PLAYER", "Value2": "Remake"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdMapIs, Grimoire", "Tag": "Map", "Text": "Map is", "Value1": "ultratyndarius", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "GO"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdBlank3, G<PERSON>oire", "Text": "...", "Alpha": 1, "R": 220, "G": 220, "B": 220}, {"$type": "Grimoire.Botting.Commands.Misc.CmdPacket, Grimoire", "Packet": "{\"t\":\"xt\",\"b\":{\"r\":-1,\"o\":{\"cmd\":\"updateQuest\",\"iValue\":30,\"iIndex\":412}}}", "SpamTimes": 1, "Delay": 1000, "ForClient": true}, {"$type": "Grimoire.Botting.Commands.Map.CmdJ<PERSON>n, Grimoire", "Map": "ultratyndarius-9099", "Cell": "Enter", "Pad": "Spawn"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdBlank3, G<PERSON>oire", "Text": "...", "Alpha": 1, "R": 220, "G": 220, "B": 220}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "GO"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdMonsterInRoom, Grimoire", "Tag": "Monster", "Text": "Is in room", "Value1": "Ultra Fire Orb", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "A-UFO"}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdMonsterInRoom, Grimoire", "Tag": "Monster", "Text": "Is in room", "Value1": "Ultra Avatar Tyndarius", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "A-AVATAR"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdBlank3, G<PERSON>oire", "Text": "...", "Alpha": 1, "R": 220, "G": 220, "B": 220}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "S"}, {"$type": "Grimoire.Botting.Commands.Combat.CmdUseSkill, Grimoire", "Skill": {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "Lingering Light", "Index": "1"}, "Targeted": true}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdHealthLessThan, Grimoire", "Tag": "This player", "Text": "Health is less than", "Value1": "2600", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Combat.CmdUseSkill, Grimoire", "Skill": {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "Wicked Purgatory", "Index": "2"}}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdPlayersHPLessThan, Grimoire", "Tag": "Player", "Text": "Health is less than", "Value1": "[PLAYER]", "Value2": "70"}, {"$type": "Grimoire.Botting.Commands.Combat.CmdUseSkill, Grimoire", "Skill": {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "Wicked Purgatory", "Index": "2"}}, {"$type": "Grimoire.Botting.Commands.Combat.CmdUseSkill, Grimoire", "Skill": {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "Light Blast", "Index": "3"}, "Targeted": true}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "GO"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdBlank3, G<PERSON>oire", "Text": "...", "Alpha": 1, "R": 220, "G": 220, "B": 220}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "A-UFO"}, {"$type": "Grimoire.Botting.Commands.Combat.CmdAttack, Grimoire", "Monster": "Ultra Fire Orb", "UseSkill": true}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "S"}, {"$type": "Grimoire.Botting.Commands.Misc.CmdBlank3, G<PERSON>oire", "Text": "...", "Alpha": 1, "R": 220, "G": 220, "B": 220}, {"$type": "Grimoire.Botting.Commands.Misc.CmdLabel, Grimoire", "Name": "A-AVATAR"}, {"$type": "Grimoire.Botting.Commands.Combat.CmdAttack, Grimoire", "Monster": "Ultra Avatar Tyndarius", "UseSkill": true}, {"$type": "Grimoire.Botting.Commands.Misc.CmdGotoLabel, Grimoire", "Label": "S"}]}, "Skills": {"$type": "System.Collections.Generic.List`1[[Grimoire.Game.Data.Skill, Grimoire]], mscorlib", "$values": []}, "Quests": {"$type": "System.Collections.Generic.List`1[[Grimoire.Game.Data.Quest, Grimoire]], mscorlib", "$values": []}, "Author": "", "Description": "{\\rtf1\\ansi\\ansicpg1252\\deff0\\deflang2057{\\fonttbl{\\f0\\fnil\\fcharset0 Microsoft Sans Serif;}}\r\n{\\colortbl ;\\red220\\green220\\blue220;}\r\n\\viewkind4\\uc1\\pard\\cf1\\b\\f0\\fs29 This is where information about a bot will be shown in RichTextFormat\\par\r\n}\r\n", "Boosts": {"$type": "System.Collections.Generic.List`1[[Grimoire.Game.Data.InventoryItem, Grimoire]], mscorlib", "$values": []}, "Drops": {"$type": "System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib", "$values": []}, "Server": {"$type": "Grimoire.Game.Data.Server, Grimoire", "sName": "Twilly", "iPort": 5588}, "ExitCombatBeforeRest": true, "ExitCombatBeforeQuest": true, "EnablePickup": true, "RelogDelay": 5000, "SkipDelayIndexIf": true, "InfiniteAttackRange": true, "DisableAnimations": true, "WalkSpeed": 8, "NotifyUponDrop": {"$type": "System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib", "$values": []}, "RestMp": 60, "RestHp": 60, "RestartUponDeath": true, "Items": {"$type": "System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib", "$values": []}, "DropDelay": 500, "FollowCheck": true, "FollowName": "remake"}