{"$type": "Grimoire.Botting.Configuration, Grimoire", "Commands": {"$type": "System.Collections.Generic.List`1[[Grimoire.Botting.IBotCommand, Grimoire]], mscorlib", "$values": [{"$type": "Grimoire.Botting.Commands.Combat.CmdShortHunt, Grimoire", "Map": "mobius-a", "Cell": "Slugfit", "Pad": "Bottom", "Monster": "*", "MonId": "", "ItemName": "Slugfit Horn", "ItemType": 1, "Quantity": "5", "AfterKills": 5, "QuestId": "", "DelayAfterKill": 500, "BlankFirst": true}, {"$type": "Grimoire.Botting.Commands.Misc.Statements.CmdMapIsNot, Grimoire", "Tag": "Map", "Text": "Map is not", "Value1": "terc<PERSON><PERSON><PERSON><PERSON>", "Value2": ""}, {"$type": "Grimoire.Botting.Commands.Map.CmdJ<PERSON>n, Grimoire", "Map": "citadel", "Cell": "m22", "Pad": "Left"}, {"$type": "Grimoire.Botting.Commands.Combat.CmdShortHunt, Grimoire", "Map": "tercessuinotlim-a", "Cell": "m2", "Pad": "Left", "Monster": "*", "MonId": "", "ItemName": "Makai Fang", "ItemType": 1, "Quantity": "5", "AfterKills": 5, "QuestId": "", "DelayAfterKill": 500}, {"$type": "Grimoire.Botting.Commands.Combat.CmdShortHunt, Grimoire", "Map": "hydra-a", "Cell": "Rune2", "Pad": "Left", "Monster": "*", "ItemName": "Imp Flame", "ItemType": 1, "Quantity": "3", "AfterKills": 5, "DelayAfterKill": 500, "BlankFirst": true}, {"$type": "Grimoire.Botting.Commands.Combat.CmdShortHunt, Grimoire", "Map": "faerie-a", "Cell": "End", "Pad": "Center", "Monster": "Cyclops <PERSON>lord", "ItemName": "<PERSON><PERSON><PERSON>", "ItemType": 1, "Quantity": "3", "AfterKills": 5, "DelayAfterKill": 500, "BlankFirst": true}, {"$type": "Grimoire.Botting.Commands.Combat.CmdShortHunt, Grimoire", "Map": "greenguardwest-a", "Cell": "West12", "Pad": "Up", "Monster": "*", "MonId": "", "ItemName": "Wereboar Tusk", "ItemType": 1, "Quantity": "2", "AfterKills": 5, "QuestId": "", "DelayAfterKill": 500, "BlankFirst": true}]}, "Skills": {"$type": "System.Collections.Generic.List`1[[Grimoire.Game.Data.Skill, Grimoire]], mscorlib", "$values": [{"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "Atramentous Shade", "Index": "1"}, {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "Wicked Purgatory", "Index": "2"}, {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "Depraved Empowerment", "Index": "3"}, {"$type": "Grimoire.Game.Data.<PERSON>, Grimoire", "Text": "Anathema", "Index": "4"}]}, "Quests": {"$type": "System.Collections.Generic.List`1[[Grimoire.Game.Data.Quest, Grimoire]], mscorlib", "$values": [{"$type": "Grimoire.Game.Data.Quest, Grimoire", "QuestID": 6697, "Text": "6697", "IsInProgress": true}]}, "Author": "Author", "Description": "{\\rtf1\\ansi\\ansicpg1252\\deff0\\deflang2057{\\fonttbl{\\f0\\fnil\\fcharset0 Microsoft Sans Serif;}}\r\n{\\colortbl ;\\red220\\green220\\blue220;}\r\n\\viewkind4\\uc1\\pard\\cf1\\b\\f0\\fs29 Ez Nulgath Birthday Farm\\par\r\n}\r\n", "Boosts": {"$type": "System.Collections.Generic.List`1[[Grimoire.Game.Data.InventoryItem, Grimoire]], mscorlib", "$values": []}, "Drops": {"$type": "System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib", "$values": ["Unidentified 13", "Tainted Gem", "Dark Crystal Shard", "Diamond of Nulgath", "Voucher of Nulgath", "Voucher of Nulgath (non-mem)", "Totem of Nulgath", "Gem of Nulgath", "<PERSON><PERSON>", "Blood Gem of the Archfiend", "Archfiend's Birthday Cake", "Fiendish Caladbolg", "Essence of Nulgath"]}, "Server": {"$type": "Grimoire.Game.Data.Server, Grimoire", "sName": "<PERSON><PERSON>", "iPort": 22970}, "ExitCombatBeforeRest": true, "ExitCombatBeforeQuest": true, "EnablePickup": true, "EnableRejection": true, "AutoRelogin": true, "RelogDelay": 5000, "BotDelay": 500, "SkipDelayIndexIf": true, "InfiniteAttackRange": true, "DisableAnimations": true, "WalkSpeed": 8, "NotifyUponDrop": {"$type": "System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib", "$values": []}, "RestMp": 60, "RestHp": 60, "RestartUponDeath": true, "Items": {"$type": "System.Collections.Generic.List`1[[System.String, mscorlib]], mscorlib", "$values": []}, "DropDelay": 1000}